{"/api/auth/register/route": "app/api/auth/register/route.js", "/api/auth/signin/route": "app/api/auth/signin/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/signup/route": "app/api/auth/signup/route.js", "/api/certificates/route": "app/api/certificates/route.js", "/api/class-enrollments/[id]/route": "app/api/class-enrollments/[id]/route.js", "/api/chapters/[id]/route": "app/api/chapters/[id]/route.js", "/api/class-enrollments/route": "app/api/class-enrollments/route.js", "/api/classes/[id]/route": "app/api/classes/[id]/route.js", "/api/chapters/route": "app/api/chapters/route.js", "/api/classes/route": "app/api/classes/route.js", "/api/courses/[id]/route": "app/api/courses/[id]/route.js", "/api/courses/route": "app/api/courses/route.js", "/api/enrollments/route": "app/api/enrollments/route.js", "/api/institutions/[id]/route": "app/api/institutions/[id]/route.js", "/api/enrollments/[id]/route": "app/api/enrollments/[id]/route.js", "/api/institutions/route": "app/api/institutions/route.js", "/api/modules/[id]/route": "app/api/modules/[id]/route.js", "/api/questions/[id]/route": "app/api/questions/[id]/route.js", "/api/modules/route": "app/api/modules/route.js", "/api/questions/route": "app/api/questions/route.js", "/api/quizzes/[id]/route": "app/api/quizzes/[id]/route.js", "/api/quizzes/route": "app/api/quizzes/route.js", "/api/reports/route": "app/api/reports/route.js", "/api/subscriptions/route": "app/api/subscriptions/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/users/route": "app/api/users/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/icon.png/route": "app/icon.png/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/page": "app/page.js", "/auth/sign-in/[[...sign-in]]/page": "app/auth/sign-in/[[...sign-in]]/page.js", "/auth/sign-up/[[...sign-up]]/page": "app/auth/sign-up/[[...sign-up]]/page.js", "/(students-page)/my-courses/page": "app/(students-page)/my-courses/page.js", "/(students-page)/courses/page": "app/(students-page)/courses/page.js", "/(course-view)/my-courses/[courseId]/exam/results/page": "app/(course-view)/my-courses/[courseId]/exam/results/page.js", "/(course-view)/my-courses/[courseId]/exam/page": "app/(course-view)/my-courses/[courseId]/exam/page.js", "/(course-view)/my-courses/[courseId]/page": "app/(course-view)/my-courses/[courseId]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/kanban/page": "app/dashboard/kanban/page.js", "/dashboard/product/[productId]/page": "app/dashboard/product/[productId]/page.js", "/dashboard/profile/[[...profile]]/page": "app/dashboard/profile/[[...profile]]/page.js", "/dashboard/product/page": "app/dashboard/product/page.js", "/dashboard/admin/institutions/[id]/page": "app/dashboard/admin/institutions/[id]/page.js", "/dashboard/admin/institutions/new/page": "app/dashboard/admin/institutions/new/page.js", "/dashboard/admin/institutions/page": "app/dashboard/admin/institutions/page.js", "/dashboard/admin/subscriptions/page": "app/dashboard/admin/subscriptions/page.js", "/dashboard/admin/users/[id]/page": "app/dashboard/admin/users/[id]/page.js", "/dashboard/admin/page": "app/dashboard/admin/page.js", "/dashboard/admin/users/new/page": "app/dashboard/admin/users/new/page.js", "/dashboard/admin/users/page": "app/dashboard/admin/users/page.js", "/dashboard/overview/@pie_stats/page": "app/dashboard/overview/@pie_stats/page.js", "/dashboard/overview/@bar_stats/page": "app/dashboard/overview/@bar_stats/page.js", "/dashboard/overview/@area_stats/page": "app/dashboard/overview/@area_stats/page.js", "/dashboard/overview/@sales/page": "app/dashboard/overview/@sales/page.js", "/dashboard/student/certificates/page": "app/dashboard/student/certificates/page.js", "/dashboard/student/page": "app/dashboard/student/page.js", "/dashboard/student/courses/[id]/page": "app/dashboard/student/courses/[id]/page.js", "/dashboard/student/courses/page": "app/dashboard/student/courses/page.js", "/dashboard/student/progress/page": "app/dashboard/student/progress/page.js", "/dashboard/teacher/classes/[id]/students/page": "app/dashboard/teacher/classes/[id]/students/page.js", "/dashboard/teacher/classes/[id]/page": "app/dashboard/teacher/classes/[id]/page.js", "/dashboard/teacher/classes/page": "app/dashboard/teacher/classes/page.js", "/dashboard/teacher/courses/[id]/page": "app/dashboard/teacher/courses/[id]/page.js", "/dashboard/teacher/classes/[id]/courses/page": "app/dashboard/teacher/classes/[id]/courses/page.js", "/dashboard/teacher/classes/new/page": "app/dashboard/teacher/classes/new/page.js", "/dashboard/teacher/courses/generate/page": "app/dashboard/teacher/courses/generate/page.js", "/dashboard/teacher/courses/new/page": "app/dashboard/teacher/courses/new/page.js", "/dashboard/teacher/courses/page": "app/dashboard/teacher/courses/page.js", "/dashboard/teacher/page": "app/dashboard/teacher/page.js", "/dashboard/teacher/reports/page": "app/dashboard/teacher/reports/page.js"}