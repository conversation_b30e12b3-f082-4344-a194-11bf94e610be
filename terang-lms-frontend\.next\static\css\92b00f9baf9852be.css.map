{"version": 3, "sources": ["webpack://_N_E/src/app/globals.css"], "names": [], "mappings": "AAAA,iEAAiE,CACjE,kBAAkB,oIAAoI,4BAA4B,kBAAkB,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,CAAC,cAAc,CAAC,cAAc,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,+BAA+B,CAAC,8BAA8B,CAAC,8BAA8B,CAAC,8BAA8B,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,2BAA2B,CAAC,4BAA4B,CAAC,6BAA6B,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,+BAA+B,CAAC,4BAA4B,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,6BAA6B,CAAC,gCAAgC,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,2BAA2B,CAAC,iCAAiC,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,2BAA2B,CAAC,6BAA6B,CAAC,0BAA0B,CAAC,gCAAgC,CAAC,8BAA8B,CAAC,+BAA+B,CAAC,gCAAgC,CAAC,4BAA4B,CAAC,6BAA6B,CAAC,8BAA8B,CAAC,2BAA2B,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,uBAAuB,CAAC,+BAA+B,CAAC,+BAA+B,CAAC,6BAA6B,CAAC,gCAAgC,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,uBAAuB,CAAC,CAAC,CAAC,aAAa,YAAY,wHAAwH,CAAC,uGAAuG,CAAC,sCAAsC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,2CAA2C,CAAC,yCAAyC,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,yCAAyC,CAAC,yCAAyC,CAAC,yCAAyC,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,2CAA2C,CAAC,4CAA4C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,yCAAyC,CAAC,wCAAwC,CAAC,0CAA0C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,0CAA0C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,uCAAuC,CAAC,yCAAyC,CAAC,yCAAyC,CAAC,wCAAwC,CAAC,uCAAuC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,2CAA2C,CAAC,0CAA0C,CAAC,4CAA4C,CAAC,2CAA2C,CAAC,4CAA4C,CAAC,4CAA4C,CAAC,4CAA4C,CAAC,4CAA4C,CAAC,4CAA4C,CAAC,4CAA4C,CAAC,0CAA0C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,wCAAwC,CAAC,mCAAmC,CAAC,oCAAoC,CAAC,oCAAoC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,kBAAkB,CAAC,uCAAuC,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,mBAAmB,CAAC,wCAAwC,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,eAAe,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,uBAAuB,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,oCAAoC,CAAC,iCAAiC,CAAC,qCAAqC,CAAC,sCAAsC,CAAC,qDAAqD,CAAC,wDAAwD,CAAC,mCAAmC,CAAC,aAAa,CAAC,cAAc,CAAC,mBAAmB,CAAC,kCAAkC,CAAC,4DAA4D,CAAC,sCAAsC,CAAC,2CAA2C,CAAC,4BAA4B,CAAC,CAAC,YAAY,4BAA4B,qBAAqB,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,uBAAuB,qBAAqB,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,6BAA6B,CAAC,UAAU,CAAC,eAAe,CAAC,mJAAmJ,CAAC,iEAAiE,CAAC,qEAAqE,CAAC,uCAAuC,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,CAAC,oBAAoB,wCAAwC,CAAC,gCAAgC,CAAC,kBAAkB,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,aAAa,CAAiE,+BAA+B,CAAC,uBAAuB,CAAC,SAAS,kBAAkB,CAAC,kBAAkB,uIAAuI,CAAC,sEAAsE,CAAC,0EAA0E,CAAC,aAAa,CAAC,MAAM,aAAa,CAAC,QAAQ,uBAAuB,CAAC,aAAa,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,aAAa,CAAC,IAAI,SAAS,CAAC,MAAM,aAAa,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,gBAAgB,YAAY,CAAC,SAAS,uBAAuB,CAAC,QAAQ,iBAAiB,CAAC,WAAW,eAAe,CAAC,+CAA+C,qBAAqB,CAAC,aAAa,CAAC,UAAU,cAAc,CAAC,WAAW,CAAC,sCAAsC,YAAY,CAAC,6BAA6B,CAAC,+BAA+B,CAAC,sBAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,sBAAsB,CAAC,eAAe,CAAC,uBAAuB,YAAY,CAAC,6BAA6B,CAAC,+BAA+B,CAAC,sBAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,sBAAsB,CAAC,eAAe,CAAC,8CAA8C,kBAAkB,CAAC,qDAAqD,yBAAyB,CAAC,uBAAuB,qBAAqB,CAAC,cAAc,SAAS,CAAC,yFAAyF,cAAc,kBAAkB,CAAC,4CAA8C,cAAc,sDAAsD,CAAC,CAAC,CAAC,SAAS,eAAe,CAAC,4BAA4B,uBAAuB,CAAC,8BAA8B,cAAc,CAAC,kBAAkB,CAAC,wBAAwB,mBAAmB,CAAC,uCAAuC,SAAS,CAAyC,2DAAmC,eAAe,CAAqD,sEAAkC,eAAe,CAAoD,wEAAqC,eAAe,CAAsD,+EAA0C,eAAe,CAAC,uCAAuC,eAAe,CAAC,iBAAiB,eAAe,CAAC,6DAA6D,iBAAiB,CAAC,uBAAuB,iBAAiB,CAAyC,wDAA4B,WAAW,CAAC,2CAA2C,sBAAsB,CAAC,EAAE,0BAA0B,CAAC,yBAAyB,CAAC,4CAA8C,EAAE,4DAA4D,CAAC,CAAC,KAAK,kCAAkC,CAAC,uBAAuB,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,mBAAmB,0BAA0B,CAAC,0BAA0B,iCAAiC,CAAC,qBAAqB,mBAAmB,CAAC,WAAW,iBAAiB,CAAC,SAAS,kBAAkB,CAAC,SAAS,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAmB,eAAe,CAAC,mBAAlC,iBAA6D,CAAC,OAAO,cAAc,CAAC,UAAU,iBAAiB,CAAC,QAAQ,eAAe,CAAC,QAAQ,eAAe,CAAC,SAAS,4BAA4B,CAAC,WAAW,mCAAmC,CAAC,WAAW,kCAAkC,CAAC,WAAW,4BAA4B,CAAC,QAAQ,2BAA2B,CAAC,QAAQ,2BAA2B,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,UAAU,OAAO,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,QAAQ,2BAA2B,CAAC,iBAAiB,SAAS,CAAC,aAAa,OAAO,CAAC,cAAc,OAAO,CAAC,cAAc,OAAO,CAAC,UAAU,QAAQ,CAAC,aAAa,8BAA8B,CAAC,UAAU,6BAA6B,CAAC,UAAU,6BAA6B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,mBAAmB,WAAW,CAAC,WAAW,8BAA8B,CAAC,WAAW,8BAA8B,CAAC,UAAU,6BAA6B,CAAC,UAAU,6BAA6B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,QAAQ,2BAA2B,CAAC,QAAQ,2BAA2B,CAAC,WAAW,QAAQ,CAAC,QAAQ,2BAA2B,CAAC,QAAQ,2BAA2B,CAAC,eAAe,QAAQ,CAAC,SAAS,iBAAiB,CAAC,KAAK,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,SAAS,aAAa,CAAC,YAAY,oBAAoB,CAAC,SAAS,SAAS,CAAC,YAAY,yBAAyB,CAAC,eAAe,gBAAgB,CAAC,aAAa,mBAAmB,CAAC,YAAY,sBAAsB,CAAC,aAAa,gBAAgB,CAAC,WAAW,UAAU,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,OAAO,qCAAqC,CAAC,SAAS,qCAAqC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,SAAS,kBAAkB,CAAC,SAAS,oCAAoC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,QAAQ,2CAA2C,CAAC,SAAS,kCAAkC,CAAC,MAAM,iCAAiC,CAAC,SAAS,mCAAmC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,OAAO,kCAAkC,CAAC,OAAO,kCAAkC,CAAC,OAAO,kCAAkC,CAAC,SAAS,4CAA4C,CAAC,SAAS,eAAe,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,SAAS,iBAAiB,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,OAAO,qCAAqC,CAAC,OAAO,qCAAqC,CAAC,OAAO,mCAAmC,CAAC,UAAU,qCAAqC,CAAC,OAAO,mCAAmC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,SAAS,gBAAgB,CAAC,cAAc,oBAAoF,CAAC,4BAAhE,2BAA2B,CAAC,mBAAmB,CAAC,eAAkH,CAAlG,cAAc,oBAAoF,CAAC,cAAc,oBAAoB,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,eAAe,CAAC,OAAO,aAAa,CAAC,UAAU,gBAAgB,CAAC,MAAM,YAAY,CAAC,MAAM,YAAY,CAAC,QAAQ,YAAY,CAAC,QAAQ,cAAc,CAAC,cAAc,oBAAoB,CAAC,aAAa,mBAAmB,CAAC,OAAO,aAAa,CAAC,eAAe,qBAAqB,CAAC,YAAY,kBAAkB,CAAC,WAAW,iBAAiB,CAAC,sBAAsB,oBAAoB,CAAC,iBAAiB,gBAAgB,CAAC,aAAa,iBAAiB,CAAC,eAAe,cAAc,CAAC,cAAc,gCAAgC,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,WAAW,8BAA8B,CAAC,+BAA+B,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,WAAW,8BAA8B,CAAC,+BAA+B,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,WAAW,UAAU,CAAC,WAAW,CAAC,KAAK,6BAA6B,CAAC,QAAQ,+BAA+B,CAAC,KAAK,6BAA6B,CAAC,QAAQ,+BAA+B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,gBAAgB,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY,WAAW,CAAC,YAAY,WAAW,CAAC,aAAa,YAAY,CAAC,aAAa,YAAY,CAAC,aAAa,YAAY,CAAC,aAAa,YAAY,CAAC,aAAa,YAAY,CAAC,2BAA2B,0BAA0B,CAAC,0BAA0B,yBAAyB,CAAC,2BAA2B,0BAA0B,CAAC,sDAAsD,mDAAmD,CAAC,4CAA4C,yCAAyC,CAAC,QAAQ,WAAW,CAAC,OAAO,kBAAkB,CAAC,QAAQ,WAAW,CAAC,MAAM,UAAU,CAAC,UAAU,YAAY,CAAC,OAAO,aAAa,CAAC,yDAAyD,6DAA6D,CAAC,0DAA0D,8DAA8D,CAAC,mDAAmD,uDAAuD,CAAC,SAAS,iCAAiC,CAAC,UAAU,kCAAkC,CAAC,UAAU,kCAAkC,CAAC,UAAU,kCAAkC,CAAC,UAAU,kCAAkC,CAAC,qBAAqB,mBAAmB,CAAC,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,CAAC,iBAAiB,gBAAgB,CAAC,iBAAiB,gBAAgB,CAAC,+BAA+B,8BAA8B,CAAC,YAAY,eAAe,CAAC,SAAS,iCAAiC,CAAC,SAAS,iCAAiC,CAAC,UAAU,kCAAkC,CAAC,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,CAAC,iBAAiB,gBAAgB,CAAC,iBAAiB,gBAAgB,CAAC,+BAA+B,8BAA8B,CAAC,YAAY,eAAe,CAAC,cAAc,gBAAgB,CAAC,WAAW,iBAAiB,CAAC,2CAA2C,8CAA8C,CAAC,uBAAuB,0BAA0B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,QAAQ,8BAA8B,CAAC,QAAQ,SAAS,CAAC,KAAK,4BAA4B,CAAC,QAAQ,8BAA8B,CAAC,QAAQ,cAAc,CAAC,KAAK,4BAA4B,CAAC,QAAQ,SAAS,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,2CAA2C,yCAAyC,CAAC,WAAW,SAAS,CAAC,eAAe,YAAY,CAAC,gBAAgB,aAAa,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,QAAQ,UAAU,CAAC,OAAO,iBAAiB,CAAC,QAAQ,UAAU,CAAC,OAAO,iBAAiB,CAAC,MAAM,SAAS,CAAC,4BAA4B,+BAA+B,CAAC,WAAW,8BAA8B,CAAC,WAAW,8BAA8B,CAAC,WAAW,8BAA8B,CAAC,WAAW,8BAA8B,CAAC,WAAW,8BAA8B,CAAC,iBAAiB,eAAe,CAAC,8BAA8B,2BAA2B,CAAC,YAAY,cAAc,CAAC,WAAW,qBAAqB,CAAC,UAAU,6BAA6B,CAAC,YAAY,cAAc,CAAC,UAAU,6BAA6B,CAAC,UAAU,6BAA6B,CAAC,UAAU,6BAA6B,CAAC,SAAS,gCAAgC,CAAC,SAAS,gCAAgC,CAAC,SAAS,gCAAgC,CAAC,SAAS,gCAAgC,CAAC,UAAU,iCAAiC,CAAC,UAAU,iCAAiC,CAAC,gBAAgB,cAAc,CAAC,iBAAiB,eAAe,CAAC,iBAAiB,eAAe,CAAC,iBAAiB,eAAe,CAAC,iBAAiB,eAAe,CAAC,+CAA+C,2CAA2C,CAAC,YAAY,cAAc,CAAC,QAAQ,MAAM,CAAC,WAAW,SAAS,CAAC,yBAAyB,aAAa,CAAC,iBAAiB,WAAW,CAAC,gBAAgB,mBAAmB,CAAC,iBAAiB,wBAAwB,CAAC,0DAA0D,mEAAmE,CAAC,2DAA2D,oEAAoE,CAAC,wDAAwD,iEAAiE,CAAC,qDAAqD,8DAA8D,CAAC,qDAAqD,8DAA8D,CAAC,oDAAoD,6DAA6D,CAAC,qDAAqD,8DAA8D,CAAC,mBAAmB,wCAA6F,CAAC,oCAArD,oDAAgJ,CAA3F,iBAAiB,qBAA0E,CAAC,uBAAuB,qBAA0E,CAAC,uCAArD,oDAA8I,CAAzF,gBAAgB,oBAAyE,CAAC,mBAAmB,wCAAwC,CAAC,oDAAoD,CAAC,mBAAmB,mDAAmD,CAAC,8DAA8D,CAAC,kBAAkB,wCAA6F,CAAC,yCAArD,oDAAsJ,CAAjG,uBAAuB,qBAA0E,CAAC,qCAAqC,iCAAiC,CAAC,oDAAoD,CAAC,WAAW,iBAAiB,CAAC,iBAAiB,CAAC,iBAA0D,CAAC,sBAAzC,wCAAkJ,CAAzG,WAAW,iBAAiB,CAAC,iBAAiB,CAAC,iBAA0D,CAAC,UAAU,WAAW,CAAC,WAAW,YAAY,CAAC,YAAY,aAAa,CAAC,WAAW,qGAAqG,CAAC,gBAAgB,+BAA+B,CAAC,qBAAqB,6CAA6C,CAAC,YAAY,8NAA8N,CAAC,cAAc,6BAA6B,CAAC,eAAe,8BAA8B,CAAC,cAAc,6BAA6B,CAAC,gBAAgB,cAAc,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,oBAAoB,kBAAkB,CAAC,gBAAgB,cAAc,CAAC,aAAa,WAAW,CAAC,YAAY,iBAAiB,CAAC,aAAa,WAAW,CAAC,WAAW,qBAAqB,CAAC,aAAa,wBAAwB,CAAC,aAAa,0CAA0C,CAAC,aAAa,wCAAwC,CAAC,cAAc,yCAAyC,CAAC,aAAa,2CAA2C,CAAC,cAAc,uBAAuB,CAAC,WAAW,oBAAoB,CAAC,WAAW,oBAAoB,CAAC,eAAe,qBAAqB,CAAC,eAAe,0BAA0B,CAAC,aAAa,6CAA6C,CAAC,aAAa,6CAA6C,CAAC,aAAa,6CAA6C,CAAC,aAAa,6CAA6C,CAAC,aAAa,6CAA6C,CAAC,aAAa,6CAA6C,CAAC,qBAAqB,2BAA2B,CAAC,yBAAyB,4BAA4B,CAAC,UAAU,qBAAqB,CAAC,kBAAkB,6BAA6B,CAAC,UAAU,kBAAkB,CAAC,WAAW,cAAc,CAAC,oBAAoB,kBAAkB,CAAC,cAAc,kBAAkB,CAAC,WAAW,oBAAoB,CAAC,aAAa,sBAAsB,CAAC,eAAe,mBAAmB,CAAC,gBAAgB,4BAA4B,CAAC,iBAAiB,6BAA6B,CAAC,gBAAgB,sBAAsB,CAAC,aAAa,wBAAwB,CAAC,eAAe,0BAA0B,CAAC,qBAAqB,mBAAmB,CAAC,OAAO,0BAA0B,CAAC,UAAU,2BAA2B,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,QAAQ,2BAA2B,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,sCAAsC,sBAAsB,CAAC,sDAAsD,CAAC,8DAA8D,CAAC,SAAS,iCAAiC,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,sCAAsC,sBAAsB,CAAC,2EAA2E,CAAC,mFAAmF,CAAC,YAAY,+BAA+B,CAAC,YAAY,qBAAqB,CAAC,kBAAkB,qBAAqB,CAAC,UAAU,sBAAsB,CAAC,kBAAkB,CAAC,eAAe,CAAC,eAAe,aAAa,CAAC,iBAAiB,eAAe,CAAC,iBAAiB,eAAe,CAAC,mBAAmB,iBAAiB,CAAC,iBAAiB,eAAe,CAAC,SAAS,oBAAoB,CAAC,aAAa,+BAA+B,CAAC,aAAa,+BAA+B,CAAC,qBAAqB,mBAAmB,CAAC,iBAAiB,iBAAiB,CAAC,iBAAiB,iBAAiB,CAAC,qBAAqB,qBAAqB,CAAC,cAAc,2BAA0B,CAAC,YAAY,2BAA2B,CAAC,YAAY,uCAAuC,CAAC,cAAc,eAAe,CAAC,YAAY,uCAAuC,CAAC,YAAY,uCAAuC,CAAC,YAAY,8BAA8B,CAAC,cAAc,oCAAoC,CAAC,qCAAqC,CAAC,eAAe,gDAAgD,CAAC,WAAW,8BAA8B,CAAC,iCAAiC,CAAC,cAAc,iDAAiD,CAAC,oDAAoD,CAAC,QAAQ,mCAAmC,CAAC,gBAAgB,CAAC,UAAU,mCAAmC,CAAC,cAAc,CAAC,UAAU,mCAAmC,CAAC,gBAAgB,CAAC,UAAU,mCAAmC,CAAC,gBAAgB,CAAC,mBAAmB,mCAAmC,CAAC,kBAAkB,CAAC,UAAU,yCAAyC,CAAC,sBAAsB,CAAC,UAAU,uCAAuC,CAAC,oBAAoB,CAAC,YAAY,uCAAuC,CAAC,oBAAoB,CAAC,UAAU,yCAAyC,CAAC,sBAAsB,CAAC,UAAU,0CAA0C,CAAC,uBAAuB,CAAC,YAAY,0CAA0C,CAAC,uBAAuB,CAAC,UAAU,wCAAwC,CAAC,qBAAqB,CAAC,YAAY,wCAAwC,CAAC,qBAAqB,CAAC,eAAe,wBAAwB,CAAC,mBAAmB,CAAC,aAAa,sBAAsB,CAAC,iBAAiB,CAAC,2BAA2B,gCAAgC,CAAC,iCAAiC,+BAA+B,CAAC,kBAAkB,mCAAmC,CAAC,kBAAkB,mCAAmC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,kCAAkC,0BAA0B,CAAC,4CAA8C,mBAAmB,6DAA6D,CAAC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,iBAAiB,kCAAkC,CAAC,kBAAkB,mCAAmC,CAAC,kBAAkB,mCAAmC,CAAC,kBAAkB,mCAAmC,CAAC,kBAAkB,mCAAmC,CAAC,kBAAkB,mCAAmC,CAAC,cAAc,yBAAyB,CAAC,6BAA6B,oCAAoC,CAAC,4CAA8C,6BAA6B,uEAAuE,CAAC,CAAC,6BAA6B,oCAAoC,CAAC,4CAA8C,6BAA6B,uEAAuE,CAAC,CAAC,mBAAmB,oCAAoC,CAAC,oCAAoC,2BAA2B,CAAC,4CAA8C,oBAAoB,8DAA8D,CAAC,CAAC,mBAAmB,oCAAoC,CAAC,mBAAmB,oCAAoC,CAAC,gBAAgB,iCAAiC,CAAC,gBAAgB,iCAAiC,CAAC,gBAAgB,iCAAiC,CAAC,gBAAgB,iCAAiC,CAAC,gBAAgB,iCAAiC,CAAC,kBAAkB,6BAA6B,CAAC,uBAAuB,kCAAkC,CAAC,gBAAgB,iCAAiC,CAAC,oBAAoB,kBAAkB,CAAC,cAAc,+BAA+B,CAAC,mBAAmB,oCAAoC,CAAC,mBAAmB,oCAAoC,CAAC,mBAAmB,oCAAoC,CAAC,sBAAsB,sBAAsB,CAAC,mBAAmB,uCAAuC,CAAC,mBAAmB,uCAAuC,CAAC,oBAAoB,wCAAwC,CAAC,sBAAsB,uBAAuB,CAAC,mBAAmB,gCAAgC,CAAC,6BAA6B,mCAAmC,CAAC,0BAA0B,8BAA8B,CAAC,4CAA8C,eAAe,iEAAiE,CAAC,CAAC,aAAa,sCAAsC,CAAC,cAAc,uCAAuC,CAAC,kCAAkC,kCAAkC,CAAC,4CAA8C,mBAAmB,qEAAqE,CAAC,CAAC,UAAU,mCAAmC,CAAC,cAAc,sBAAsB,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,cAAc,0BAA0B,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,YAAY,qCAAqC,CAAC,gBAAgB,0BAA0B,CAAC,4CAA8C,gBAAgB,wEAAwE,CAAC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,WAAW,8BAA8B,CAAC,SAAS,4BAA4B,CAAC,YAAY,6BAA6B,CAAC,gBAAgB,mCAAmC,CAAC,eAAe,kCAAkC,CAAC,YAAY,qCAAqC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,aAAa,sCAAsC,CAAC,iBAAiB,0BAA0B,CAAC,4CAA8C,iBAAiB,yEAAyE,CAAC,CAAC,cAAc,uCAAuC,CAAC,cAAc,uCAAuC,CAAC,cAAc,uCAAuC,CAAC,cAAc,uCAAuC,CAAC,wBAAwB,6BAA6B,CAAC,4CAA8C,cAAc,gEAAgE,CAAC,CAAC,cAAc,uCAAuC,CAAC,eAAe,wCAAwC,CAAC,YAAY,+BAA+B,CAAC,2BAA2B,+BAA+B,CAAC,4CAA8C,eAAe,iEAAiE,CAAC,CAAC,gBAAgB,+BAA+B,CAAC,4CAA8C,gBAAgB,kEAAkE,CAAC,CAAC,cAAc,uCAAuC,CAAC,eAAe,wCAAwC,CAAC,eAAe,wCAAwC,CAAC,eAAe,wCAAwC,CAAC,WAAW,oCAAoC,CAAC,YAAY,qCAAqC,CAAC,YAAY,qCAAqC,CAAC,YAAY,qCAAqC,CAAC,gCAAgC,iCAAiC,CAAC,4CAA8C,kBAAkB,oEAAoE,CAAC,CAAC,YAAY,+BAA+B,CAAC,mBAAmB,sCAAsC,CAAC,gBAAgB,sBAAsB,CAAC,UAAU,mCAAmC,CAAC,cAAc,0BAA0B,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,cAAc,sBAAsB,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,cAAc,sBAAsB,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,cAAc,0BAA0B,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,cAAc,uCAAuC,CAAC,eAAe,wCAAwC,CAAC,eAAe,wCAAwC,CAAC,eAAe,wCAAwC,CAAC,eAAe,wCAAwC,CAAC,gBAAgB,gCAAgC,CAAC,6DAA+D,gBAAgB,yCAAyC,CAAC,CAAC,gBAAgB,0DAA0D,CAAC,gBAAgB,6BAA6B,CAAC,6DAA+D,gBAAgB,sCAAsC,CAAC,CAA4E,kCAA3D,0DAAiL,CAAtH,kBAAkB,yCAAoG,CAAC,mBAAmB,8CAAyG,CAAC,sCAA3D,0DAAwL,CAA7H,mBAAmB,+CAA0G,CAAC,kBAAkB,wCAAmG,CAAC,oCAA3D,0DAA8K,CAAnH,kBAAkB,sCAAiG,CAAC,cAAc,uCAAuC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,iBAAiB,oCAAoC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,iBAAiB,iCAAiC,CAAC,4CAA8C,iBAAiB,mEAAmE,CAAC,CAAC,iBAAiB,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,gBAAgB,yCAAyC,CAAC,wLAAwL,CAAC,kBAAkB,8BAA8B,CAAC,wLAAwL,CAAC,YAAY,qCAAqC,CAAC,wLAAwL,CAAC,gBAAgB,yCAAyC,CAAC,wLAAwL,CAAC,iBAAiB,0CAA0C,CAAC,wLAAwL,CAAC,cAAc,uCAAuC,CAAC,qNAAqN,CAAC,gDAAgD,CAAC,WAAW,oCAAoC,CAAC,qNAAqN,CAAC,gDAAgD,CAAC,UAAU,mCAAmC,CAAC,wLAAwL,CAAC,YAAY,qCAAqC,CAAC,wLAAwL,CAAC,aAAa,sCAAsC,CAAC,wLAAwL,CAAC,cAAc,uCAAuC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,cAAc,uCAAuC,CAAC,wLAAwL,CAAC,gBAAgB,+BAA+B,CAAC,4CAA8C,gBAAgB,kEAAkE,CAAC,CAAC,gBAAgB,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,cAAc,uCAAuC,CAAC,wLAAwL,CAAC,gBAAgB,4BAA4B,CAAC,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,cAAc,4BAA4B,CAAC,oBAAoB,CAAC,cAAc,iBAAiB,CAAC,iBAAiB,sBAAsB,CAAC,uBAAuB,4BAA4B,CAAC,cAAc,mBAAmB,CAAC,gBAAgB,kBAAkB,CAAC,cAAc,gBAAgB,CAAC,OAAO,wCAAwC,CAAC,KAAK,8BAA8B,CAAC,OAAO,wCAAwC,CAAC,KAAK,8BAA8B,CAAC,QAAQ,gCAAgC,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,MAAM,+BAA+B,CAAC,MAAM,WAAW,CAAC,MAAM,qCAAqC,CAAC,SAAS,uCAAuC,CAAC,MAAM,qCAAqC,CAAC,SAAS,uCAAuC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,QAAQ,8CAA8C,CAAC,MAAM,oCAAoC,CAAC,SAAS,qCAAqC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,OAAO,qCAAqC,CAAC,OAAO,qCAAqC,CAAC,QAAQ,4CAA4C,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,OAAO,mCAAmC,CAAC,OAAO,mCAAmC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,OAAO,qCAAqC,CAAC,OAAO,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,OAAO,oCAAoC,CAAC,aAAa,iBAAiB,CAAC,WAAW,eAAe,CAAC,YAAY,gBAAgB,CAAC,cAAc,qBAAqB,CAAC,WAAW,4BAA4B,CAAC,WAAW,4BAA4B,CAAC,UAAU,yBAAyB,CAAC,0DAA0D,CAAC,UAAU,yBAAyB,CAAC,0DAA0D,CAAC,UAAU,yBAAyB,CAAC,0DAA0D,CAAC,UAAU,yBAAyB,CAAC,0DAA0D,CAAC,WAAW,0BAA0B,CAAC,2DAA2D,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,kBAAkB,eAAe,CAAC,eAAe,cAAc,CAAC,gBAAgB,eAAe,CAAC,cAAc,cAAc,CAAC,aAAa,CAAC,iBAAiB,mCAAmC,CAAC,kCAAkC,CAAC,eAAe,iCAAiC,CAAC,gCAAgC,CAAC,YAAY,yCAAyC,CAAC,oCAAoC,CAAC,WAAW,wCAAwC,CAAC,mCAAmC,CAAC,gBAAgB,6CAA6C,CAAC,wCAAwC,CAAC,aAAa,0CAA0C,CAAC,qCAAqC,CAAC,aAAa,0CAA0C,CAAC,qCAAqC,CAAC,eAAe,4CAA4C,CAAC,uCAAuC,CAAC,gBAAgB,mCAAmC,CAAC,oCAAoC,CAAC,eAAe,kCAAkC,CAAC,mCAAmC,CAAC,iBAAiB,oCAAoC,CAAC,qCAAqC,CAAC,cAAc,iBAAiB,CAAC,aAAa,wBAAwB,CAAC,mBAAmB,kBAAkB,CAAC,qBAAqB,oBAAoB,CAAC,+BAA+B,wBAAwB,CAAC,wBAAwB,8BAA8B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,sBAAsB,4BAA4B,CAAC,cAAc,kBAAkB,CAAC,kBAAkB,wBAAwB,CAAC,sCAAsC,uBAAuB,CAAC,4CAA8C,qBAAqB,0DAA0D,CAAC,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,kDAAkD,6BAA6B,CAAC,4CAA8C,2BAA2B,gEAAgE,CAAC,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,yBAAyB,+BAA+B,CAAC,cAAc,oBAAoB,CAAC,yBAAyB,+BAA+B,CAAC,kBAAkB,oBAAoB,CAAC,4CAA8C,kBAAkB,uDAAuD,CAAC,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,cAAc,0BAA0B,CAAC,cAAc,0BAA0B,CAAC,cAAc,0BAA0B,CAAC,cAAc,0BAA0B,CAAC,0DAA0D,iCAAiC,CAAC,4CAA8C,+BAA+B,oEAAoE,CAAC,CAAC,sDAAsD,+BAA+B,CAAC,4CAA8C,6BAA6B,kEAAkE,CAAC,CAAC,iCAAiC,uCAAuC,CAAC,cAAc,0BAA0B,CAAC,cAAc,0BAA0B,CAAC,kBAAkB,WAAW,CAAC,YAAY,wBAAwB,CAAC,gBAAgB,eAAe,CAAC,4CAA8C,gBAAgB,2DAA2D,CAAC,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,iBAAiB,6BAA6B,CAAC,YAAY,yBAAyB,CAAC,WAAW,wBAAwB,CAAC,QAAQ,iBAAiB,CAAC,cAAc,iCAAiC,CAAC,4IAA4I,CAAC,gBAAgB,mCAAmC,CAAC,WAAW,8BAA8B,CAAC,oBAAoB,yBAAyB,CAAC,aAAa,kCAAkC,CAAC,iCAAiC,CAAC,WAAW,SAAS,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,WAAW,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,aAAa,SAAS,CAAC,QAAQ,wGAA0O,CAAC,oBAAlI,iIAA8U,CAA5M,YAAY,8DAAgM,CAAC,qDAAqD,uEAAyM,CAAC,gEAAlI,iIAA4X,CAA1P,WAAW,6GAA+O,CAAC,WAAW,2GAA6O,CAAC,wBAAlI,iIAAsS,CAApK,aAAa,qBAAuJ,CAAC,WAAW,wGAA0O,CAAC,sBAAlI,iIAA6X,CAA3P,WAAW,8GAAgP,CAAC,WAAW,wDAA0L,CAAC,iBAAlI,iIAA8X,CAA5P,MAAM,oHAAsP,CAAC,QAAQ,oHAAsP,CAAC,gBAAlI,iIAAgY,CAA9P,QAAQ,oHAAsP,CAAC,QAAQ,oHAAoH,CAAC,iIAAiI,CAAC,eAAe,qCAAqC,CAAC,gBAAgB,sCAAsC,CAAC,cAAc,8BAA8B,CAAC,cAAc,oCAAoC,CAAC,eAAe,2BAA2B,CAAC,4CAA8C,eAAe,8DAA8D,CAAC,CAAC,mBAAmB,mCAAmC,CAAC,cAAc,oCAAoC,CAAC,iBAAiB,uCAAuC,CAAC,wBAAwB,wCAAwC,CAAC,gBAAgB,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,gBAAgB,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,SAAS,qCAAqC,CAAC,iBAAiB,CAAC,SAAS,8BAAgN,CAAC,yBAAlL,iLAA0f,CAAxU,gBAAgB,kFAAkF,CAAC,mDAAqO,CAAC,QAAQ,iLAAiL,CAAC,kBAAkB,uCAAuC,CAAC,+QAA+Q,CAAC,uQAAuQ,CAAC,YAAY,6TAA6T,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,kCAAkC,oCAAoC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,mCAAmC,oCAAoC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,gCAAgC,kCAAkC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,uCAAuC,wCAAwC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,8BAA8B,gCAAgC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,sBAAsB,yBAAyB,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,gBAAgB,uBAAuB,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,mBAAmB,6JAA6J,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,oBAAoB,2BAA2B,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,mBAAmB,8BAA8B,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,sBAAsB,oDAAoD,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,iBAAiB,wBAAwB,CAAC,cAAc,iBAAiB,CAAC,uBAAuB,CAAC,cAAc,iBAAiB,CAAC,uBAAuB,CAAC,cAAc,iBAAiB,CAAC,uBAAuB,CAAC,eAAe,gBAAgB,CAAC,sBAAsB,CAAC,aAAa,4BAA4B,CAAC,6CAA6C,CAAC,aAAa,gBAAgB,CAAC,iCAAiC,CAAC,UAAU,yBAAyB,CAAC,0CAA0C,CAAC,WAAW,oBAAoB,CAAC,cAAc,uBAAuB,CAAC,kBAAkB,CAAC,aAAa,wBAAwB,CAAC,gBAAgB,CAAC,YAAY,oBAAoB,CAAC,SAAS,oBAAoB,CAAC,YAAY,qBAAqB,CAAC,wBAAwB,6CAA6C,CAAC,qBAAqB,gDAAgD,CAAC,yFAAyF,SAAS,CAAC,qBAAqB,qDAAqD,sCAAsC,CAAC,uDAAuD,2BAA2B,CAAC,2EAA2E,SAAS,CAAC,CAAC,gIAAgI,8BAA8B,CAAC,0HAA0H,oCAAoC,CAAC,qFAAqF,kCAAkC,CAAC,sFAAsF,YAAY,CAAC,wFAAwF,sCAAsC,CAAC,uCAAuC,CAAC,0GAA0G,+BAA+B,CAAC,6IAA6I,gEAAgE,CAAC,kJAAkJ,sEAAsE,CAAC,+FAA+F,eAAe,CAAC,qFAAqF,wCAAwC,CAAC,qFAAqF,wCAAwC,CAAC,yFAAyF,SAAS,CAAC,sIAAsI,mCAAmC,CAAC,qIAAqI,kCAAkC,CAAC,6FAA6F,4BAA4B,CAAC,uGAAuG,uCAAuC,CAAC,oDAAoD,CAAC,6FAA6F,mBAAmB,CAAC,oFAAoF,UAAU,CAAC,0EAA0E,6BAA6B,CAAC,0EAA0E,yCAAyC,CAAC,sBAAsB,CAAC,0EAA0E,2BAA2B,CAAC,8EAA8E,aAAa,CAAC,4EAA4E,wCAAwC,CAAC,qBAAqB,CAAC,8EAA8E,aAAa,CAAC,uGAAuG,YAAY,CAAC,0FAA0F,2BAA2B,CAAC,sFAAsF,mCAAmC,CAAC,gBAAgB,CAAC,qGAAqG,kCAAkC,CAAC,yFAAyF,wGAAwG,CAAC,iIAAiI,CAAC,6IAA6I,aAAa,CAAC,sHAAsH,QAAQ,CAAC,qHAAqH,mCAAmC,CAAC,6HAA6H,eAAe,CAAC,wHAAwH,uCAAuC,CAAC,oHAAoH,mCAAmC,CAAC,gBAAgB,CAAC,wHAAwH,+BAA+B,CAAC,qIAAqI,+BAA+B,CAAC,oHAAoH,wGAAwG,CAAC,iIAAiI,CAAC,0HAA0H,iBAAiB,CAAC,uBAAuB,CAAC,qBAAqB,gGAAgG,sCAAsC,CAAC,CAAC,gEAAgE,kBAAkB,CAAC,wDAAwD,UAAU,CAAC,wDAAwD,UAAU,CAAC,4HAA4H,sCAAsC,CAAC,wGAAwG,4BAA4B,CAAC,8FAA8F,4BAA4B,CAAC,2FAA2F,0BAA0B,CAAC,mCAAmC,+BAA+B,CAAC,kCAAkC,+BAA+B,CAAC,gDAAgD,+BAA+B,CAAC,+CAA+C,+BAA+B,CAAC,yCAAyC,mBAAmB,CAAC,iCAAiC,6BAA6B,CAAC,sCAAsC,mCAAmC,CAAC,cAAc,CAAC,4CAA4C,sBAAsB,CAAC,qCAAqC,wBAAwB,CAAC,yDAAyD,CAAC,yCAAyC,0CAA0C,CAAC,qCAAqC,CAAC,6CAA6C,uBAAuB,CAAC,iDAAiD,6BAA6B,CAAC,uBAAuB,yBAAyB,CAAC,iBAAiB,CAAC,uBAAuB,yBAAyB,CAAC,6BAA6B,CAAC,wBAAwB,yBAAyB,CAAC,kCAAkC,CAAC,wBAAwB,yBAAyB,CAAC,QAAQ,CAAC,kBAAkB,yBAAyB,CAAC,4BAA4B,CAAC,wBAAwB,yBAAyB,CAAC,SAAS,CAAC,gCAAgC,yBAAyB,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,gHAAgH,yBAAyB,CAAC,SAAS,CAAC,iCAAiC,gDAAgD,CAAC,mDAAmD,CAAC,6BAA6B,wCAAwC,CAAC,qBAAqB,CAAC,+BAA+B,iDAAiD,CAAC,oDAAoD,CAAC,6BAA6B,0CAA0C,CAAC,qBAAqB,CAAC,8BAA8B,wCAAwC,CAAC,qBAAqB,CAAC,qCAAqC,iBAAiB,CAAC,iCAAiC,UAAU,CAAC,qBAAqB,8BAA8B,UAAU,CAAC,uBAAuB,WAAW,CAAC,8BAA8B,kCAAkC,CAAC,8BAA8B,kCAAkC,CAAC,0CAA0C,oCAAoC,CAAC,4CAA8C,0CAA0C,uEAAuE,CAAC,CAAC,0CAA0C,mCAAmC,CAAC,4CAA4C,qCAAqC,CAAC,wBAAwB,8BAA8B,CAAC,uBAAuB,mCAAmC,CAAC,yBAAyB,qCAAqC,CAAC,0BAA0B,sCAAsC,CAAC,0BAA0B,sCAAsC,CAAC,iCAAiC,mCAAmC,CAAC,4CAA8C,iCAAiC,sEAAsE,CAAC,CAAC,yBAAyB,qCAAqC,CAAC,0BAA0B,sCAAsC,CAAC,0BAA0B,sCAAsC,CAAC,0BAA0B,sCAAsC,CAAC,2BAA2B,uCAAuC,CAAC,2BAA2B,uCAAuC,CAAC,2BAA2B,uCAAuC,CAAC,2BAA2B,uCAAuC,CAAC,kDAAkD,6BAA6B,CAAC,4CAA8C,2BAA2B,gEAAgE,CAAC,CAAC,2BAA2B,6BAA6B,CAAC,4CAA8C,2BAA2B,gEAAgE,CAAC,CAAC,2BAA2B,6BAA6B,CAAC,4CAA8C,2BAA2B,gEAAgE,CAAC,CAAC,qDAAqD,+BAA+B,CAAC,4CAA8C,4BAA4B,iEAAiE,CAAC,CAAC,6BAA6B,+BAA+B,CAAC,4CAA8C,6BAA6B,kEAAkE,CAAC,CAAC,6BAA6B,+BAA+B,CAAC,4CAA8C,6BAA6B,kEAAkE,CAAC,CAAC,yBAAyB,qCAAqC,CAAC,yBAAyB,qCAAqC,CAAC,+BAA+B,iCAAiC,CAAC,4CAA8C,+BAA+B,oEAAoE,CAAC,CAAC,gCAAgC,sCAAsC,CAAC,wBAAwB,oCAAoC,CAAC,6BAA6B,sBAAsB,CAAC,uBAAuB,mCAAmC,CAAC,2BAA2B,sBAAsB,CAAC,4CAA8C,2BAA2B,sEAAsE,CAAC,CAAC,4BAA4B,wCAAwC,CAAC,4BAA4B,wCAAwC,CAAC,8CAA8C,0BAA0B,CAAC,qCAAqC,8BAA8B,CAAC,4BAA4B,2BAA2B,CAAC,4BAA4B,2BAA2B,CAAC,+BAA+B,wBAAwB,CAAC,8BAA8B,uBAAuB,CAAC,4BAA4B,2BAA2B,CAAC,4BAA4B,2BAA2B,CAAC,4BAA4B,2BAA2B,CAAC,oCAAoC,6BAA6B,CAAC,sCAAsC,+BAA+B,CAAC,2BAA2B,0BAA0B,CAAC,6CAA6C,sCAAsC,CAAC,yBAAyB,wBAAwB,CAAC,wBAAwB,8BAA8B,CAAC,yBAAyB,UAAU,CAAC,0BAA0B,SAAS,CAAC,yBAAyB,8DAAgM,CAAC,2FAAlI,iIAA6Y,CAA3Q,kEAAkE,uEAAyM,CAAC,wBAAwB,6GAA+O,CAAC,gDAAlI,iIAA0Y,CAAxQ,wBAAwB,8GAAgP,CAAC,qBAAqB,oHAAoH,CAAC,iIAAiI,CAAC,iHAAiH,+BAA+B,CAAC,6CAA6C,yBAAyB,CAAC,sCAAsC,CAAC,CAAC,mBAAmB,UAAU,CAAC,8CAA8C,+BAA+B,CAAC,6BAA6B,iCAAiC,CAAC,iCAAiC,kBAAkB,CAAC,wBAAwB,8BAA8B,CAAC,yBAAyB,+BAA+B,CAAC,qCAAqC,8BAA8B,CAAC,sCAAsC,+BAA+B,CAAC,qBAAqB,oHAAsP,CAAC,0CAAlI,iIAA6Y,CAA3Q,qBAAqB,oHAAsP,CAAC,qBAAqB,oHAAoH,CAAC,iIAAiI,CAAC,4CAA4C,kCAAkC,CAAC,4BAA4B,qCAAqC,CAAC,2BAA2B,oCAAoC,CAAC,wBAAwB,2BAA2B,CAAC,4BAA4B,0BAA0B,CAAC,yGAAyG,CAAC,4BAA4B,0BAA0B,CAAC,yGAAyG,CAAC,6BAA6B,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,6BAA6B,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,2BAA2B,uBAAuB,CAAC,kBAAkB,CAAC,mCAAmC,UAAU,CAAC,0CAA0C,wBAAwB,CAAC,qCAAqC,oHAAoH,CAAC,iIAAiI,CAAC,qCAAqC,oHAAoH,CAAC,iIAAiI,CAAC,qCAAqC,oHAAoH,CAAC,iIAAiI,CAAC,2CAA2C,oHAAoH,CAAC,iIAAiI,CAAC,gEAAgE,kCAAkC,CAAC,4CAA8C,gEAAgE,qEAAqE,CAAC,CAAC,mDAAmD,kCAAkC,CAAC,4CAA8C,mDAAmD,qEAAqE,CAAC,CAAC,oFAAoF,2BAA2B,CAAC,4CAA8C,4CAA4C,8DAA8D,CAAC,CAAC,4CAA4C,0BAA0B,CAAC,yGAAyG,CAAC,4CAA4C,0BAA0B,CAAC,yGAAyG,CAAC,6CAA6C,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,6CAA6C,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,wCAAwC,qCAAqC,CAAC,iBAAiB,CAAC,2CAA2C,uBAAuB,CAAC,kBAAkB,CAAC,+BAA+B,+BAA+B,CAAC,4CAA8C,+BAA+B,kEAAkE,CAAC,CAAC,kCAAkC,sCAAsC,CAAC,wCAAwC,+BAA+B,CAAC,+CAA+C,sCAAsC,CAAC,wCAAwC,mBAAmB,CAAC,uCAAuC,kBAAkB,CAAC,mCAAmC,cAAc,CAAC,gCAAgC,sBAAsB,CAAC,iBAAiB,CAAC,+BAA+B,UAAU,CAAC,gCAAgC,SAAS,CAAC,kEAAkE,eAAe,CAAC,oEAAoE,eAAe,CAAC,yCAAyC,UAAU,CAAC,qFAAqF,8BAA8B,CAAC,mEAAmE,+BAA+B,CAAC,wEAAwE,+CAA+C,CAAC,kCAAkC,iCAAiC,CAAC,kCAAkC,uCAAuC,CAAC,+BAA+B,qCAAqC,CAAC,+BAA+B,qCAAqC,CAAC,wDAAwD,mBAAmB,CAAC,+CAA+C,UAAU,CAAC,qDAAqD,+BAA+B,CAAC,uDAAuD,kCAAkC,CAAC,4CAA8C,uDAAuD,qEAAqE,CAAC,CAAC,8CAA8C,8BAA8B,CAAC,+CAA+C,+BAA+B,CAAC,2DAA2D,8BAA8B,CAAC,0DAA0D,6BAA6B,CAAC,4DAA4D,+BAA+B,CAAC,gDAAgD,SAAS,CAAC,+CAA+C,UAAU,CAAC,sDAAsD,wBAAwB,CAAC,wDAAwD,8BAA8B,CAAC,4CAA8C,wDAAwD,iEAAiE,CAAC,CAAC,wDAAwD,+BAA+B,CAAC,4CAA8C,wDAAwD,iEAAiE,CAAC,CAAC,4DAA4D,sCAAsC,CAAC,sDAAsD,0CAA0C,CAAC,qCAAqC,CAAC,iEAAiE,8BAA8B,CAAC,yEAAyE,sCAAsC,CAAC,uDAAuD,oHAAoH,CAAC,iIAAiI,CAAC,wDAAwD,2BAA2B,CAAC,4CAA8C,wDAAwD,8DAA8D,CAAC,CAAC,qBAAqB,iEAAiE,8BAA8B,CAAC,CAAC,iEAAiE,8BAA8B,CAAC,8FAA8F,+BAA+B,CAAC,gGAAgG,kCAAkC,CAAC,4CAA8C,gGAAgG,qEAAqE,CAAC,CAAC,uDAAuD,mBAAmB,CAAC,8CAA8C,UAAU,CAAC,kEAAkE,mBAAmB,CAAC,yDAAyD,UAAU,CAAC,yDAAyD,wBAAwB,CAAC,kCAAkC,mCAAmC,CAAC,yEAAyE,8CAA8C,CAAC,4EAA4E,iDAAiD,CAAC,oEAAoE,6CAA6C,CAAC,uEAAuE,gDAAgD,CAAC,0DAA0D,8NAA8N,CAAC,uDAAuD,oBAAoB,CAAC,uDAAuD,6NAA6N,CAAC,oDAAoD,mBAAmB,CAAC,uEAAuE,+BAA+B,CAAC,uEAAuE,WAAW,CAAC,uEAAuE,UAAU,CAAC,gEAAgE,6BAA6B,CAAC,mEAAmE,WAAW,CAAC,qEAAqE,kCAAkC,CAAC,mEAAmE,8BAA8B,CAAC,mEAAmE,UAAU,CAAC,mEAAmE,UAAU,CAAC,qEAAqE,qBAAqB,CAAC,qFAAqF,UAAU,CAAC,uFAAuF,UAAU,CAAC,yFAAyF,qBAAqB,CAAC,oGAAoG,yBAAyB,CAAC,2BAA2B,CAAC,iGAAiG,yBAAyB,CAAC,6BAA6B,CAAC,oGAAoG,yBAAyB,CAAC,UAAU,CAAC,2GAA2G,yBAAyB,CAAC,uCAAuC,CAAC,oDAAoD,CAAC,+GAA+G,yBAAyB,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,+DAA+D,6BAA6B,CAAC,wDAAwD,8BAA8B,CAAC,qEAAqE,8BAA8B,CAAC,wDAAwD,uCAAuC,CAAC,oDAAoD,CAAC,8DAA8D,gDAAgD,CAAC,qDAAqD,wCAAwC,CAAC,oDAAoD,CAAC,4DAA4D,6CAA6C,CAAC,sDAAsD,uCAAuC,CAAC,oDAAoD,CAAC,6DAA6D,gDAAgD,CAAC,mDAAmD,wCAAwC,CAAC,oDAAoD,CAAC,2DAA2D,6CAA6C,CAAC,gDAAgD,6BAA6B,CAAC,sCAAsC,6BAA6B,CAAC,gGAAgG,wBAAwB,CAAC,4CAA8C,gGAAgG,2DAA2D,CAAC,CAAC,kEAAkE,sCAAsC,CAAC,0DAA0D,CAAC,iEAAiE,iCAAiC,CAAC,4CAA8C,iEAAiE,mEAAmE,CAAC,CAAC,iEAAiE,wLAAwL,CAAC,yDAAyD,4BAA4B,CAAC,wLAAwL,CAAC,2DAA2D,wDAAwD,CAAC,iIAAiI,CAAC,0FAA0F,8BAA8B,CAAC,uGAAuG,oHAAoH,CAAC,iIAAiI,CAAC,6GAA6G,uBAAuB,CAAC,kBAAkB,CAAC,8EAA8E,oBAAoB,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,eAAe,CAAC,sEAAsE,YAAY,CAAC,sEAAsE,6BAA6B,CAAC,8EAA8E,kBAAkB,CAAC,uEAAuE,0BAA0B,CAAC,0DAA0D,kCAAkC,CAAC,4DAA4D,uBAAuB,CAAC,sDAAsD,wGAAwG,CAAC,iIAAiI,CAAC,gFAAgF,iCAAiC,CAAC,oDAAoD,CAAC,8EAA8E,+BAA+B,CAAC,6DAA6D,2BAA2B,CAAC,0EAA0E,mCAAmC,CAAC,yDAAyD,+BAA+B,CAAC,sEAAsE,+BAA+B,CAAC,iEAAiE,wOAAwO,CAAC,wDAAwD,6NAA6N,CAAC,yDAAyD,iBAAiB,CAAC,uBAAuB,CAAC,uDAAuD,mBAAmB,CAAC,wDAAwD,mBAAmB,CAAC,gEAAgE,0BAA0B,CAAC,8DAA8D,2BAA2B,CAAC,+DAA+D,0BAA0B,CAAC,6DAA6D,2BAA2B,CAAC,oKAAoK,6NAA6N,CAAC,mKAAmK,mBAAmB,CAAC,oKAAoK,mBAAmB,CAAC,wDAAwD,6NAA6N,CAAC,qDAAqD,mBAAmB,CAAC,8CAA8C,8BAA8B,CAAC,2DAA2D,8BAA8B,CAAC,+DAA+D,0OAA0O,CAAC,mDAAmD,8NAA8N,CAAC,wGAAwG,8BAA8B,CAAC,4CAA8C,sDAAsD,iEAAiE,CAAC,CAAC,qDAAqD,iCAAiC,CAAC,0DAA0D,sCAAsC,CAAC,+DAA+D,8BAA8B,CAAC,8DAA8D,6BAA6B,CAAC,uEAAuE,sCAAsC,CAAC,oDAAoD,SAAS,CAAC,qDAAqD,iBAAiB,CAAC,uBAAuB,CAAC,kDAAkD,oBAAoB,CAAC,mDAAmD,mBAAmB,CAAC,mDAAmD,oBAAoB,CAAC,6DAA6D,2BAA2B,CAAC,2DAA2D,4BAA4B,CAAC,4DAA4D,2BAA2B,CAAC,0DAA0D,4BAA4B,CAAC,+JAA+J,8NAA8N,CAAC,8JAA8J,oBAAoB,CAAC,+JAA+J,oBAAoB,CAAC,qBAAqB,+DAA+D,8BAA8B,CAAC,uEAAuE,sCAAsC,CAAC,oFAAoF,sCAAsC,CAAC,CAAC,+DAA+D,8BAA8B,CAAC,yDAAyD,6BAA6B,CAAC,gEAAgE,uCAAuC,CAAC,oDAAoD,CAAC,2DAA2D,6BAA6B,CAAC,yDAAyD,8NAA8N,CAAC,sDAAsD,oBAAoB,CAAC,2EAA2E,wBAAwB,CAAC,0FAA0F,mCAAmC,CAAC,4CAA8C,0FAA0F,sEAAsE,CAAC,CAAC,wFAAwF,wBAAwB,CAAC,6DAA6D,wCAAwC,CAAC,mBAAmB,CAAC,4DAA4D,wDAAwD,CAAC,iIAAiI,CAAC,8EAA8E,wCAAwC,CAAC,qBAAqB,CAAC,sFAAsF,mCAAmC,CAAC,qFAAqF,6BAA6B,CAAC,kFAAkF,kCAAkC,CAAC,2FAA2F,eAAe,CAAC,yFAAyF,oCAAoC,CAAC,qCAAqC,CAAC,qFAAqF,uCAAuC,CAAC,oBAAoB,CAAC,kFAAkF,kCAAkC,CAAC,+EAA+E,2BAA2B,CAAC,+EAA+E,SAAS,CAAC,iFAAiF,yCAAyC,CAAC,sBAAsB,CAAC,oFAAoF,kCAAkC,CAAC,kFAAkF,4BAA4B,CAAC,iFAAiF,SAAS,CAAC,mFAAmF,wCAAwC,CAAC,qBAAqB,CAAC,gFAAgF,mCAAmC,CAAC,4EAA4E,0BAA0B,CAAC,4EAA4E,qCAAqC,CAAC,qFAAqF,eAAe,CAAC,mFAAmF,wCAAwC,CAAC,uCAAuC,CAAC,+EAA+E,0CAA0C,CAAC,uBAAuB,CAAC,yBAAyB,UAAU,oCAAoC,CAAC,UAAU,oCAAoC,CAAC,WAAW,aAAa,CAAC,UAAU,YAAY,CAAC,YAAY,YAAY,CAAC,UAAU,8BAA8B,CAAC,iBAAiB,YAAY,CAAC,iBAAiB,WAAW,CAAC,eAAe,8BAA8B,CAAC,eAAe,8BAA8B,CAAC,qBAAqB,eAAe,CAAC,cAAc,6BAA6B,CAAC,cAAc,6BAA6B,CAAC,cAAc,6BAA6B,CAAC,iBAAiB,6CAA6C,CAAC,cAAc,kBAAkB,CAAC,iBAAiB,wBAAwB,CAAC,cAAc,4BAA4B,CAAC,WAAW,0BAA0B,CAAC,WAAW,0BAA0B,CAAC,yCAAyC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,gBAAgB,uCAAuC,CAAC,kBAAkB,CAAC,cAAc,wCAAwC,CAAC,qBAAqB,CAAC,SAAS,8BAA8B,CAAC,SAAS,8BAA8B,CAAC,UAAU,qCAAqC,CAAC,UAAU,qCAAqC,CAAC,UAAU,qCAAqC,CAAC,UAAU,oCAAoC,CAAC,UAAU,kCAAkC,CAAC,aAAa,sCAAsC,CAAC,WAAW,qCAAqC,CAAC,UAAU,qCAAqC,CAAC,aAAa,qCAAqC,CAAC,eAAe,eAAe,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,4KAA4K,6BAA6B,CAAC,CAAC,yBAAyB,cAAc,iBAAiB,CAAC,gBAAgB,yBAAyB,CAAC,gBAAgB,yBAAyB,CAAC,WAAW,aAAa,CAAC,UAAU,YAAY,CAAC,UAAU,YAAY,CAAC,YAAY,YAAY,CAAC,kBAAkB,oBAAoB,CAAC,UAAU,6BAA6B,CAAC,UAAU,6BAA6B,CAAC,yDAAyD,iDAAiD,CAAC,YAAY,UAAU,CAAC,cAAc,6BAA6B,CAAC,YAAY,MAAM,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,6CAA6C,CAAC,gBAAgB,uCAAuC,CAAC,oBAAoB,CAAC,gBAAgB,wCAAwC,CAAC,mBAAmB,CAAC,UAAU,qCAAqC,CAAC,UAAU,qCAAqC,CAAC,UAAU,kCAAkC,CAAC,UAAU,qCAAqC,CAAC,UAAU,mCAAmC,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,eAAe,SAAS,CAAC,+EAA+E,6BAA6B,CAAC,gFAAgF,kCAAkC,CAAC,sFAAsF,uCAAuC,CAAC,qFAAqF,wGAAwG,CAAC,iIAAiI,CAAC,0JAA0J,kCAAkC,CAAC,yBAAyB,yBAAyB,CAAC,YAAY,CAAC,CAAC,yBAAyB,gBAAgB,yBAAyB,CAAC,gBAAgB,yBAAyB,CAAC,gBAAgB,yBAAyB,CAAC,UAAU,oCAAoC,CAAC,WAAW,qCAAqC,CAAC,WAAW,aAAa,CAAC,UAAU,YAAY,CAAC,YAAY,YAAY,CAAC,YAAY,WAAW,CAAC,YAAY,SAAS,CAAC,UAAU,6BAA6B,CAAC,UAAU,6BAA6B,CAAC,eAAe,8BAA8B,CAAC,cAAc,6BAA6B,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,6CAA6C,CAAC,iBAAiB,wBAAwB,CAAC,mBAAmB,0BAA0B,CAAC,WAAW,0BAA0B,CAAC,SAAS,8BAA8B,CAAC,UAAU,+BAA+B,CAAC,UAAU,qCAAqC,CAAC,UAAU,qCAAqC,CAAC,WAAW,qCAAqC,CAAC,eAAe,eAAe,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,CAAC,yBAAyB,gBAAgB,yBAAyB,CAAC,gBAAgB,yBAAyB,CAAC,iBAAiB,6CAA6C,CAAC,CAAC,kCAAkC,6BAA6B,yBAAyB,CAAC,0DAA0D,CAAC,CAAC,kCAAkC,0BAA0B,aAAa,CAAC,2BAA2B,YAAY,CAAC,CAAC,kCAAkC,yBAAyB,6CAA6C,CAAC,CAAC,kCAAkC,0BAA0B,6CAA6C,CAAC,CAAC,gCAAgC,yBAAyB,CAAC,sCAAsC,mCAAmC,CAAC,4CAA8C,sCAAsC,sEAAsE,CAAC,CAAC,gCAAgC,6BAA6B,CAAC,4CAA8C,gCAAgC,gEAAgE,CAAC,CAAC,mCAAmC,uBAAuB,CAAC,qBAAqB,8CAA8C,8BAA8B,CAAC,4CAA8C,8CAA8C,iEAAiE,CAAC,CAAC,6CAA6C,6BAA6B,CAAC,4CAA8C,6CAA6C,gEAAgE,CAAC,CAAC,CAAC,qEAAqE,kCAAkC,CAAC,4CAA8C,qEAAqE,qEAAqE,CAAC,CAAC,yEAAyE,kCAAkC,CAAC,4CAA8C,yEAAyE,qEAAqE,CAAC,CAAC,kHAAkH,kCAAkC,CAAC,4CAA8C,kHAAkH,qEAAqE,CAAC,CAAC,2EAA2E,4BAA4B,CAAC,2EAA2E,+BAA+B,CAAC,sFAAsF,0CAA0C,CAAC,kFAAkF,kCAAkC,CAAC,iFAAiF,6BAA6B,CAAC,4CAA8C,iFAAiF,gEAAgE,CAAC,CAAC,4GAA4G,mCAAmC,CAAC,4CAA8C,4GAA4G,sEAAsE,CAAC,CAAC,sGAAsG,4BAA4B,CAAC,2HAA2H,oBAAoB,CAAC,4CAA8C,2HAA2H,uDAAuD,CAAC,CAAC,yGAAyG,oBAAoB,CAAC,4FAA4F,YAAY,CAAC,yDAAyD,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,yDAAyD,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,uGAAuG,oBAAoB,CAAC,iNAAiN,iBAAiB,CAAC,+GAA+G,oBAAoB,CAAC,2DAA2D,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,2DAA2D,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,kGAAkG,YAAY,CAAC,6DAA6D,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,6DAA6D,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,0DAA0D,qCAAqC,CAAC,6DAA6D,sCAAsC,CAAC,6DAA6D,wBAAwB,CAAC,yDAAyD,CAAC,iEAAiE,0CAA0C,CAAC,qCAAqC,CAAC,2EAA2E,6BAA6B,CAAC,0CAA0C,qCAAqC,CAAC,yGAAyG,kCAAkC,CAAC,iEAAiE,6BAA6B,CAAC,iEAAiE,4BAA4B,CAAC,0CAA0C,8BAA8B,CAAC,wCAAwC,qCAAqC,CAAC,wCAAwC,oCAAoC,CAAC,+CAA+C,6BAA6B,CAAC,+CAA+C,4BAA4B,CAAC,6BAA6B,mCAAmC,CAAC,kCAAkC,CAAC,qCAAqC,mBAAmB,CAAC,2BAA2B,iBAAiB,CAAC,wBAAwB,4BAA4B,CAAC,6BAA6B,CAAC,0BAA0B,aAAa,CAAC,uCAAuC,6BAA6B,CAAC,2EAA2E,4BAA4B,CAAC,6BAA6B,CAAC,0FAA0F,6BAA6B,CAAC,wGAAwG,8BAA8B,CAAC,wBAAwB,0CAA0C,CAAC,uBAAuB,CAAC,+CAA+C,mCAAmC,CAAC,cAAc,CAAC,qEAAqE,iDAAiD,CAAC,oDAAoD,CAAC,yEAAyE,gDAAgD,CAAC,mDAAmD,CAAC,mEAAmE,uCAAuC,CAAC,kEAAkE,8BAA8B,CAAC,wFAAwF,gDAAgD,CAAC,mDAAmD,CAAC,wLAAwL,iDAAiD,CAAC,oDAAoD,CAAC,8DAA8D,oCAAoC,CAAC,+BAA+B,qCAAqC,CAAC,+BAA+B,kCAAkC,CAAC,qDAAqD,YAAY,CAAC,6DAA6D,kBAAkB,CAAC,sDAAsD,0BAA0B,CAAC,yGAAyG,kCAAkC,CAAC,iEAAiE,oBAAoB,CAAC,oDAAoD,CAAC,+BAA+B,YAAY,CAAC,qDAAqD,4BAA4B,CAAC,uDAAuD,SAAS,CAAC,oDAAoD,sBAAsB,CAAC,kBAAkB,CAAC,eAAe,CAAC,sCAAsC,mBAAmB,CAAC,yBAAyB,4BAA4B,CAAC,6BAA6B,CAAC,4BAA4B,8BAA8B,CAAC,+BAA+B,CAAC,yBAAyB,4BAA4B,CAAC,6BAA6B,CAAC,yBAAyB,+BAA+B,CAAC,sBAAsB,6BAA6B,CAAC,yBAAyB,8BAA8B,CAAC,sBAAsB,4BAA4B,CAAC,2BAA2B,aAAa,CAAC,mCAAmC,wCAAwC,CAAC,oDAAoD,CAAC,+BAA+B,kBAAkB,CAAC,wCAAwC,6BAA6B,CAAC,iDAAiD,sCAAsC,CAAC,4CAA4C,0CAA0C,CAAC,qBAAqB,CAAC,yGAAyG,YAAY,CAAC,qCAAqC,6BAA6B,CAAC,6IAA6I,YAAY,CAAC,kEAAkE,aAAa,CAAC,kHAAkH,6BAA6B,CAAC,6GAA6G,eAAe,CAAC,mHAAmH,4BAA4B,CAAC,+GAA+G,eAAe,CAAC,qBAAqB,kCAAkC,8BAA8B,CAAC,2CAA2C,mCAAmC,CAAC,4CAA8C,2CAA2C,sEAAsE,CAAC,CAAC,uCAAuC,+BAA+B,CAAC,4CAA8C,uCAAuC,kEAAkE,CAAC,CAAC,yCAAyC,iCAAiC,CAAC,4CAA8C,yCAAyC,oEAAoE,CAAC,CAAC,+CAA+C,8BAA8B,CAAC,CAAC,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,mCAAmC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,mCAAmC,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,yCAAyC,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,6BAA6B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,wBAAwB,CAAC,sBAAsB,CAAC,MAAM,6BAA6B,CAAC,6CAA6C,CAAC,0BAA0B,cAAc,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,+BAA+B,gBAAgB,CAAC,qFAAqF,oBAAoB,CAAC,qCAAqC,kCAAkC,CAAC,4CAA4C,CAAC,sDAAsD,kCAAkC,CAAC,4CAA4C,CAAC,+BAA+B,+BAA+B,CAAC,yCAAyC,CAAC,gDAAgD,+BAA+B,CAAC,yCAAyC,CAAC,mFAAmF,+BAA+B,CAAC,yCAAyC,CAAC,iCAAiC,gCAAgC,CAAC,0CAA0C,CAAC,kDAAkD,gCAAgC,CAAC,0CAA0C,CAAC,+BAA+B,4BAA4B,CAAC,kCAAkC,CAAC,4CAA4C,CAAC,gDAAgD,kCAAkC,CAAC,4CAA4C,CAAC,gPAAgP,eAAe,CAAC,yBAAyB,CAAC,2OAA2O,+BAA+B,CAAC,2IAA2I,CAAC,+HAA+H,+BAA+B,CAAC,2IAA2I,CAAC,yBAAyB,CAAC,MAAM,gBAAgB,CAAC,4BAA4B,CAAC,6BAA6B,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,yBAAyB,CAAC,qCAAqC,CAAC,4BAA4B,CAAC,qCAAqC,CAAC,0BAA0B,CAAC,uCAAuC,CAAC,sBAAsB,CAAC,mCAAmC,CAAC,2BAA2B,CAAC,oCAAoC,CAAC,sCAAsC,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,yBAAyB,CAAC,kCAAkC,CAAC,iCAAiC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,iCAAiC,CAAC,0BAA0B,CAAC,qCAAqC,CAAC,oCAAoC,CAAC,6CAA6C,CAAC,+BAA+B,CAAC,4CAA4C,CAAC,iCAAiC,CAAC,iCAAiC,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,MAAM,6BAA6B,CAAC,6BAA6B,CAAC,uBAAuB,CAAC,kCAAkC,CAAC,0BAA0B,CAAC,qCAAqC,CAAC,4BAA4B,CAAC,qCAAqC,CAAC,4BAA4B,CAAC,uCAAuC,CAAC,wBAAwB,CAAC,mCAAmC,CAAC,2BAA2B,CAAC,oCAAoC,CAAC,sCAAsC,CAAC,2BAA2B,CAAC,2BAA2B,CAAC,yBAAyB,CAAC,mCAAmC,CAAC,iCAAiC,CAAC,iCAAiC,CAAC,iCAAiC,CAAC,kCAAkC,CAAC,0BAA0B,CAAC,qCAAqC,CAAC,oCAAoC,CAAC,6CAA6C,CAAC,iCAAiC,CAAC,4CAA4C,CAAC,mCAAmC,CAAC,iCAAiC,CAAC,4BAA4B,qBAAqB,CAAC,cAAc,CAAC,4BAA4B,qBAAqB,CAAC,cAAc,CAAC,4BAA4B,SAAS,CAAC,4BAA4B,SAAS,CAAC,kBAAkB,GAAG,+CAAgD,CAAC,UAAU,CAAC,GAAG,kDAAkD,CAAC,SAAS,CAAC,CAAC,4BAA4B,yCAAyC,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,6BAA6B,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,4BAA4B,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,2BAA2B,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,8BAA8B,UAAU,CAAC,cAAc,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,sCAAsC,4BAA4B,CAAC,cAAc,CAAC,eAAgB,CAAC,qCAAqC,4BAA4B,CAAC,cAAc,CAAC,iBAAiB,CAAC,oCAAoC,4BAA4B,CAAC,cAAc,CAAC,kBAAkB,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,8BAA8B,UAAU,CAAC,cAAc,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,4BAA4B,qBAAqB,CAAC,cAAc,CAAC,kBAAkB,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,kCAAkC,qBAAqB,CAAC,cAAc,CAAC,kBAAkB,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,iCAAiC,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,6BAA6B,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,oBAAoB,UAAU,CAAC,cAAc,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,yBAAyB,UAAU,CAAC,cAAc,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,qBAAqB,UAAU,CAAC,cAAc,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,iCAAiC,qBAAqB,CAAC,cAAc,CAAC,kBAAkB,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,6BAA6B,UAAU,CAAC,cAAc,CAAC,mCAAmC,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,mCAAmC,UAAU,CAAC,cAAc,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,8BAA8B,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,oBAAoB,UAAU,CAAC,cAAc,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,uBAAwB,CAAC,CAAC,gBAAgB,OAAO,SAAS,CAAC,kBAAkB,CAAC,CAAC,iBAAiB,IAAI,UAAU,CAAC,CAAC,kBAAkB,MAAM,gDAAgD,CAAC,0BAA0B,CAAC,IAAI,gDAAgD,CAAC,cAAc,CAAC,CAAC,iBAAiB,GAAG,iCAAiC,CAAC,oMAAoM,CAAC,CAAC,gBAAgB,GAAG,gCAAgC,CAAC,8LAA8L,CAAC,CAAC,0BAA0B,GAAG,QAAQ,CAAC,GAAG,8JAA8J,CAAC,CAAC,wBAAwB,GAAG,8JAA8J,CAAC,GAAG,QAAQ,CAAC,CAAC,uBAAuB,UAAU,SAAS,CAAC,QAAQ,SAAS,CAAC", "file": "static/css/92b00f9baf9852be.css", "sourcesContent": ["/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-ordinal:initial;--tw-slashed-zero:initial;--tw-numeric-figure:initial;--tw-numeric-spacing:initial;--tw-numeric-fraction:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-content:\"\";--tw-animation-delay:0s;--tw-animation-direction:normal;--tw-animation-duration:initial;--tw-animation-fill-mode:none;--tw-animation-iteration-count:1;--tw-enter-opacity:1;--tw-enter-rotate:0;--tw-enter-scale:1;--tw-enter-translate-x:0;--tw-enter-translate-y:0;--tw-exit-opacity:1;--tw-exit-rotate:0;--tw-exit-scale:1;--tw-exit-translate-x:0;--tw-exit-translate-y:0}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,\"Liberation Mono\",\"Courier New\",monospace;--color-red-50:oklch(97.1% .013 17.38);--color-red-100:oklch(93.6% .032 17.717);--color-red-200:oklch(88.5% .062 18.334);--color-red-300:oklch(80.8% .114 19.571);--color-red-400:oklch(70.4% .191 22.216);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-red-800:oklch(44.4% .177 26.899);--color-orange-50:oklch(98% .016 73.684);--color-orange-100:oklch(95.4% .038 75.164);--color-orange-400:oklch(75% .183 55.934);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-orange-800:oklch(47% .157 37.304);--color-amber-50:oklch(98.7% .022 95.277);--color-amber-200:oklch(92.4% .12 95.746);--color-amber-500:oklch(76.9% .188 70.08);--color-amber-600:oklch(66.6% .179 58.318);--color-amber-700:oklch(55.5% .163 48.998);--color-amber-800:oklch(47.3% .137 46.201);--color-yellow-50:oklch(98.7% .026 102.212);--color-yellow-100:oklch(97.3% .071 103.193);--color-yellow-200:oklch(94.5% .129 101.54);--color-yellow-300:oklch(90.5% .182 98.111);--color-yellow-400:oklch(85.2% .199 91.936);--color-yellow-500:oklch(79.5% .184 86.047);--color-yellow-600:oklch(68.1% .162 75.834);--color-yellow-700:oklch(55.4% .135 66.442);--color-yellow-800:oklch(47.6% .114 61.907);--color-lime-50:oklch(98.6% .031 120.757);--color-lime-600:oklch(64.8% .2 131.684);--color-green-50:oklch(98.2% .018 155.826);--color-green-100:oklch(96.2% .044 156.743);--color-green-200:oklch(92.5% .084 155.995);--color-green-300:oklch(87.1% .15 154.449);--color-green-400:oklch(79.2% .209 151.711);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-green-800:oklch(44.8% .119 151.328);--color-green-900:oklch(39.3% .095 152.535);--color-sky-50:oklch(97.7% .013 236.62);--color-sky-300:oklch(82.8% .111 230.318);--color-sky-600:oklch(58.8% .158 241.966);--color-sky-900:oklch(39.1% .09 240.876);--color-blue-50:oklch(97% .014 254.604);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-400:oklch(70.7% .165 254.624);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-blue-800:oklch(42.4% .199 265.638);--color-blue-900:oklch(37.9% .146 265.522);--color-indigo-50:oklch(96.2% .018 272.314);--color-indigo-100:oklch(93% .034 272.788);--color-indigo-600:oklch(51.1% .262 276.966);--color-purple-50:oklch(97.7% .014 308.299);--color-purple-100:oklch(94.6% .033 307.174);--color-purple-200:oklch(90.2% .063 306.703);--color-purple-400:oklch(71.4% .203 305.504);--color-purple-600:oklch(55.8% .288 302.321);--color-purple-700:oklch(49.6% .265 301.924);--color-purple-800:oklch(43.8% .218 303.724);--color-slate-50:oklch(98.4% .003 247.858);--color-slate-800:oklch(27.9% .041 260.031);--color-slate-900:oklch(20.8% .042 265.755);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-100:oklch(96.7% .003 264.542);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-300:oklch(87.2% .01 258.338);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-neutral-50:oklch(98.5% 0 0);--color-neutral-500:oklch(55.6% 0 0);--color-neutral-600:oklch(43.9% 0 0);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-sm:24rem;--container-md:28rem;--container-lg:32rem;--container-xl:36rem;--container-2xl:42rem;--container-3xl:48rem;--container-4xl:56rem;--container-6xl:72rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height:calc(2.25/1.875);--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--text-5xl:3rem;--text-5xl--line-height:1;--text-6xl:3.75rem;--text-6xl--line-height:1;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--font-weight-extrabold:800;--font-weight-black:900;--tracking-tight:-.025em;--tracking-wide:.025em;--tracking-widest:.1em;--leading-tight:1.25;--leading-relaxed:1.625;--radius-xs:.125rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--drop-shadow-lg:0 4px 4px #00000026;--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-ping:ping 1s cubic-bezier(0,0,.2,1)infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--animate-bounce:bounce 1s infinite;--blur-sm:8px;--blur-md:12px;--aspect-video:16/9;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-border:var(--border)}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,\"Liberation Mono\",\"Courier New\",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*{border-color:var(--border);outline-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){*{outline-color:color-mix(in oklab,var(--ring)50%,transparent)}}body{background-color:var(--background);color:var(--foreground)}}@layer components;@layer utilities{.\\@container\\/card{container:card/inline-size}.\\@container\\/card-header{container:card-header/inline-size}.pointer-events-none{pointer-events:none}.invisible{visibility:hidden}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.inset-x-0{inset-inline:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.-top-0\\.5{top:calc(var(--spacing)*-.5)}.-top-1{top:calc(var(--spacing)*-1)}.-top-4{top:calc(var(--spacing)*-4)}.top-0{top:calc(var(--spacing)*0)}.top-1\\.5{top:calc(var(--spacing)*1.5)}.top-1\\/2{top:50%}.top-2{top:calc(var(--spacing)*2)}.top-2\\.5{top:calc(var(--spacing)*2.5)}.top-3{top:calc(var(--spacing)*3)}.top-3\\.5{top:calc(var(--spacing)*3.5)}.top-4{top:calc(var(--spacing)*4)}.top-24{top:calc(var(--spacing)*24)}.top-\\[0\\.3rem\\]{top:.3rem}.top-\\[1px\\]{top:1px}.top-\\[50\\%\\]{top:50%}.top-\\[60\\%\\]{top:60%}.top-full{top:100%}.-right-0\\.5{right:calc(var(--spacing)*-.5)}.-right-1{right:calc(var(--spacing)*-1)}.-right-4{right:calc(var(--spacing)*-4)}.right-0{right:calc(var(--spacing)*0)}.right-1{right:calc(var(--spacing)*1)}.right-2{right:calc(var(--spacing)*2)}.right-3{right:calc(var(--spacing)*3)}.right-4{right:calc(var(--spacing)*4)}.right-8{right:calc(var(--spacing)*8)}.right-\\[0\\.3rem\\]{right:.3rem}.-bottom-1{bottom:calc(var(--spacing)*-1)}.-bottom-4{bottom:calc(var(--spacing)*-4)}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-4{bottom:calc(var(--spacing)*4)}.-left-4{left:calc(var(--spacing)*-4)}.-left-8{left:calc(var(--spacing)*-8)}.left-0{left:calc(var(--spacing)*0)}.left-1{left:calc(var(--spacing)*1)}.left-1\\/2{left:50%}.left-2{left:calc(var(--spacing)*2)}.left-3{left:calc(var(--spacing)*3)}.left-\\[50\\%\\]{left:50%}.isolate{isolation:isolate}.z-5{z-index:5}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.z-99999{z-index:99999}.z-\\[-1\\]\\!{z-index:-1!important}.z-\\[1\\]{z-index:1}.col-span-4{grid-column:span 4/span 4}.col-span-full{grid-column:1/-1}.col-start-2{grid-column-start:2}.row-span-2{grid-row:span 2/span 2}.row-start-1{grid-row-start:1}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.-mx-1{margin-inline:calc(var(--spacing)*-1)}.mx-0\\.5{margin-inline:calc(var(--spacing)*.5)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-3\\.5{margin-inline:calc(var(--spacing)*3.5)}.mx-4{margin-inline:calc(var(--spacing)*4)}.mx-auto{margin-inline:auto}.my-0\\.5{margin-block:calc(var(--spacing)*.5)}.my-1{margin-block:calc(var(--spacing)*1)}.my-2{margin-block:calc(var(--spacing)*2)}.my-3{margin-block:calc(var(--spacing)*3)}.my-4{margin-block:calc(var(--spacing)*4)}.my-6{margin-block:calc(var(--spacing)*6)}.mt-0\\!{margin-top:calc(var(--spacing)*0)!important}.mt-0\\.5{margin-top:calc(var(--spacing)*.5)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-1\\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-12{margin-top:calc(var(--spacing)*12)}.mt-16{margin-top:calc(var(--spacing)*16)}.mt-20{margin-top:calc(var(--spacing)*20)}.mt-64\\!{margin-top:calc(var(--spacing)*64)!important}.mt-auto{margin-top:auto}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mr-auto{margin-right:auto}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.mb-12{margin-bottom:calc(var(--spacing)*12)}.mb-16{margin-bottom:calc(var(--spacing)*16)}.-ml-1{margin-left:calc(var(--spacing)*-1)}.-ml-1\\.5{margin-left:calc(var(--spacing)*-1.5)}.-ml-2{margin-left:calc(var(--spacing)*-2)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-4{margin-left:calc(var(--spacing)*4)}.ml-6{margin-left:calc(var(--spacing)*6)}.ml-auto{margin-left:auto}.line-clamp-1{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-2{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-3{-webkit-line-clamp:3;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.block{display:block}.contents{display:contents}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.table{display:table}.table-caption{display:table-caption}.table-cell{display:table-cell}.table-row{display:table-row}.field-sizing-content{field-sizing:content}.aspect-\\[4\\/3\\]{aspect-ratio:4/3}.aspect-auto{aspect-ratio:auto}.aspect-square{aspect-ratio:1}.aspect-video{aspect-ratio:var(--aspect-video)}.size-2{width:calc(var(--spacing)*2);height:calc(var(--spacing)*2)}.size-2\\.5{width:calc(var(--spacing)*2.5);height:calc(var(--spacing)*2.5)}.size-3{width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.size-3\\.5{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.size-7{width:calc(var(--spacing)*7);height:calc(var(--spacing)*7)}.size-8{width:calc(var(--spacing)*8);height:calc(var(--spacing)*8)}.size-9{width:calc(var(--spacing)*9);height:calc(var(--spacing)*9)}.size-full{width:100%;height:100%}.h-1{height:calc(var(--spacing)*1)}.h-1\\.5{height:calc(var(--spacing)*1.5)}.h-2{height:calc(var(--spacing)*2)}.h-2\\.5{height:calc(var(--spacing)*2.5)}.h-3{height:calc(var(--spacing)*3)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-11{height:calc(var(--spacing)*11)}.h-12{height:calc(var(--spacing)*12)}.h-16{height:calc(var(--spacing)*16)}.h-20{height:calc(var(--spacing)*20)}.h-24{height:calc(var(--spacing)*24)}.h-32{height:calc(var(--spacing)*32)}.h-48{height:calc(var(--spacing)*48)}.h-52{height:calc(var(--spacing)*52)}.h-64{height:calc(var(--spacing)*64)}.h-96{height:calc(var(--spacing)*96)}.h-\\[1\\.15rem\\]{height:1.15rem}.h-\\[1px\\]{height:1px}.h-\\[75vh\\]{height:75vh}.h-\\[90vh\\]{height:90vh}.h-\\[250px\\]{height:250px}.h-\\[280px\\]{height:280px}.h-\\[300px\\]{height:300px}.h-\\[316px\\]{height:316px}.h-\\[400px\\]{height:400px}.h-\\[calc\\(100dvh-52px\\)\\]{height:calc(100dvh - 52px)}.h-\\[calc\\(100vh-64px\\)\\]{height:calc(100vh - 64px)}.h-\\[calc\\(100vh-210px\\)\\]{height:calc(100vh - 210px)}.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\]{height:var(--radix-navigation-menu-viewport-height)}.h-\\[var\\(--radix-select-trigger-height\\)\\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-fit{height:fit-content}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.h-svh{height:100svh}.max-h-\\(--radix-context-menu-content-available-height\\){max-height:var(--radix-context-menu-content-available-height)}.max-h-\\(--radix-dropdown-menu-content-available-height\\){max-height:var(--radix-dropdown-menu-content-available-height)}.max-h-\\(--radix-select-content-available-height\\){max-height:var(--radix-select-content-available-height)}.max-h-8{max-height:calc(var(--spacing)*8)}.max-h-16{max-height:calc(var(--spacing)*16)}.max-h-32{max-height:calc(var(--spacing)*32)}.max-h-48{max-height:calc(var(--spacing)*48)}.max-h-96{max-height:calc(var(--spacing)*96)}.max-h-\\[18\\.75rem\\]{max-height:18.75rem}.max-h-\\[60vh\\]{max-height:60vh}.max-h-\\[70vh\\]{max-height:70vh}.max-h-\\[75vh\\]{max-height:75vh}.max-h-\\[80vh\\]{max-height:80vh}.max-h-\\[85vh\\]{max-height:85vh}.max-h-\\[90vh\\]{max-height:90vh}.max-h-\\[300px\\]{max-height:300px}.max-h-\\[400px\\]{max-height:400px}.max-h-\\[calc\\(100vh-12rem\\)\\]{max-height:calc(100vh - 12rem)}.max-h-full{max-height:100%}.min-h-0{min-height:calc(var(--spacing)*0)}.min-h-4{min-height:calc(var(--spacing)*4)}.min-h-16{min-height:calc(var(--spacing)*16)}.min-h-\\[32px\\]{min-height:32px}.min-h-\\[60px\\]{min-height:60px}.min-h-\\[200px\\]{min-height:200px}.min-h-\\[400px\\]{min-height:400px}.min-h-\\[calc\\(100vh-200px\\)\\]{min-height:calc(100vh - 200px)}.min-h-full{min-height:100%}.min-h-screen{min-height:100vh}.min-h-svh{min-height:100svh}.w-\\(--radix-dropdown-menu-trigger-width\\){width:var(--radix-dropdown-menu-trigger-width)}.w-\\(--sidebar-width\\){width:var(--sidebar-width)}.w-0{width:calc(var(--spacing)*0)}.w-1{width:calc(var(--spacing)*1)}.w-1\\.5{width:calc(var(--spacing)*1.5)}.w-1\\/2{width:50%}.w-2{width:calc(var(--spacing)*2)}.w-2\\.5{width:calc(var(--spacing)*2.5)}.w-2\\/3{width:66.6667%}.w-3{width:calc(var(--spacing)*3)}.w-3\\/4{width:75%}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-6{width:calc(var(--spacing)*6)}.w-7{width:calc(var(--spacing)*7)}.w-8{width:calc(var(--spacing)*8)}.w-9{width:calc(var(--spacing)*9)}.w-10{width:calc(var(--spacing)*10)}.w-12{width:calc(var(--spacing)*12)}.w-16{width:calc(var(--spacing)*16)}.w-20{width:calc(var(--spacing)*20)}.w-24{width:calc(var(--spacing)*24)}.w-28{width:calc(var(--spacing)*28)}.w-32{width:calc(var(--spacing)*32)}.w-40{width:calc(var(--spacing)*40)}.w-44{width:calc(var(--spacing)*44)}.w-48{width:calc(var(--spacing)*48)}.w-56{width:calc(var(--spacing)*56)}.w-64{width:calc(var(--spacing)*64)}.w-72{width:calc(var(--spacing)*72)}.w-80{width:calc(var(--spacing)*80)}.w-96{width:calc(var(--spacing)*96)}.w-\\[--radix-dropdown-menu-trigger-width\\]{width:--radix-dropdown-menu-trigger-width}.w-\\[1px\\]{width:1px}.w-\\[4\\.5rem\\]{width:4.5rem}.w-\\[12\\.5rem\\]{width:12.5rem}.w-\\[70px\\]{width:70px}.w-\\[80px\\]{width:80px}.w-\\[95vw\\]{width:95vw}.w-\\[100px\\]{width:100px}.w-\\[120px\\]{width:120px}.w-\\[140px\\]{width:140px}.w-\\[150px\\]{width:150px}.w-\\[160px\\]{width:160px}.w-\\[180px\\]{width:180px}.w-\\[200px\\]{width:200px}.w-\\[250px\\]{width:250px}.w-\\[300px\\]{width:300px}.w-\\[350px\\]{width:350px}.w-auto{width:auto}.w-fit{width:fit-content}.w-full{width:100%}.w-max{width:max-content}.w-px{width:1px}.max-w-\\(--skeleton-width\\){max-width:var(--skeleton-width)}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-4xl{max-width:var(--container-4xl)}.max-w-6xl{max-width:var(--container-6xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-\\[600px\\]{max-width:600px}.max-w-\\[calc\\(100\\%-2rem\\)\\]{max-width:calc(100% - 2rem)}.max-w-full{max-width:100%}.max-w-max{max-width:max-content}.max-w-md{max-width:var(--container-md)}.max-w-none{max-width:none}.max-w-sm{max-width:var(--container-sm)}.max-w-xl{max-width:var(--container-xl)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-5{min-width:calc(var(--spacing)*5)}.min-w-8{min-width:calc(var(--spacing)*8)}.min-w-9{min-width:calc(var(--spacing)*9)}.min-w-10{min-width:calc(var(--spacing)*10)}.min-w-56{min-width:calc(var(--spacing)*56)}.min-w-\\[8rem\\]{min-width:8rem}.min-w-\\[12rem\\]{min-width:12rem}.min-w-\\[120px\\]{min-width:120px}.min-w-\\[300px\\]{min-width:300px}.min-w-\\[320px\\]{min-width:320px}.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{min-width:var(--radix-select-trigger-width)}.min-w-full{min-width:100%}.flex-1{flex:1}.flex-none{flex:none}.flex-shrink-0,.shrink-0{flex-shrink:0}.flex-grow,.grow{flex-grow:1}.caption-bottom{caption-side:bottom}.border-collapse{border-collapse:collapse}.origin-\\(--radix-context-menu-content-transform-origin\\){transform-origin:var(--radix-context-menu-content-transform-origin)}.origin-\\(--radix-dropdown-menu-content-transform-origin\\){transform-origin:var(--radix-dropdown-menu-content-transform-origin)}.origin-\\(--radix-hover-card-content-transform-origin\\){transform-origin:var(--radix-hover-card-content-transform-origin)}.origin-\\(--radix-menubar-content-transform-origin\\){transform-origin:var(--radix-menubar-content-transform-origin)}.origin-\\(--radix-popover-content-transform-origin\\){transform-origin:var(--radix-popover-content-transform-origin)}.origin-\\(--radix-select-content-transform-origin\\){transform-origin:var(--radix-select-content-transform-origin)}.origin-\\(--radix-tooltip-content-transform-origin\\){transform-origin:var(--radix-tooltip-content-transform-origin)}.-translate-x-1\\/2{--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-x-px{--tw-translate-x:-1px;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-\\[-50\\%\\]{--tw-translate-x:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-px{--tw-translate-x:1px;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\\/2{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-12\\!{--tw-translate-y:calc(var(--spacing)*-12)!important;translate:var(--tw-translate-x)var(--tw-translate-y)!important}.translate-y-0\\.5{--tw-translate-y:calc(var(--spacing)*.5);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\\[-50\\%\\]{--tw-translate-y:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\\[calc\\(-50\\%_-_2px\\)\\]{--tw-translate-y:calc(-50% - 2px);translate:var(--tw-translate-x)var(--tw-translate-y)}.scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-125{--tw-scale-x:125%;--tw-scale-y:125%;--tw-scale-z:125%;scale:var(--tw-scale-x)var(--tw-scale-y)}.rotate-3{rotate:3deg}.rotate-45{rotate:45deg}.rotate-180{rotate:180deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.animate-bounce{animation:var(--animate-bounce)}.animate-caret-blink{animation:1.25s ease-out infinite caret-blink}.animate-in{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.animate-ping{animation:var(--animate-ping)}.animate-pulse{animation:var(--animate-pulse)}.animate-spin{animation:var(--animate-spin)}.cursor-default{cursor:default}.cursor-grab{cursor:grab}.cursor-move{cursor:move}.cursor-not-allowed{cursor:not-allowed}.cursor-pointer{cursor:pointer}.cursor-text{cursor:text}.touch-none{touch-action:none}.resize-none{resize:none}.snap-none{scroll-snap-type:none}.snap-center{scroll-snap-align:center}.scroll-my-1{scroll-margin-block:calc(var(--spacing)*1)}.scroll-mt-4{scroll-margin-top:calc(var(--spacing)*4)}.scroll-mt-20{scroll-margin-top:calc(var(--spacing)*20)}.scroll-py-1{scroll-padding-block:calc(var(--spacing)*1)}.list-decimal{list-style-type:decimal}.list-disc{list-style-type:disc}.list-none{list-style-type:none}.grid-flow-col{grid-auto-flow:column}.auto-rows-min{grid-auto-rows:min-content}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.grid-cols-\\[0_1fr\\]{grid-template-columns:0 1fr}.grid-rows-\\[auto_auto\\]{grid-template-rows:auto auto}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.place-items-center{place-items:center}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-around{justify-content:space-around}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.justify-items-start{justify-items:start}.gap-0{gap:calc(var(--spacing)*0)}.gap-0\\.5{gap:calc(var(--spacing)*.5)}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-2\\.5{gap:calc(var(--spacing)*2.5)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-6{gap:calc(var(--spacing)*6)}.gap-8{gap:calc(var(--spacing)*8)}.gap-12{gap:calc(var(--spacing)*12)}:where(.space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-px>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(1px*var(--tw-space-y-reverse));margin-block-end:calc(1px*calc(1 - var(--tw-space-y-reverse)))}.gap-x-4{column-gap:calc(var(--spacing)*4)}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-3>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*3)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-6>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*6)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-8>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*8)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-12>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*12)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*12)*calc(1 - var(--tw-space-x-reverse)))}.gap-y-0\\.5{row-gap:calc(var(--spacing)*.5)}.self-start{align-self:flex-start}.justify-self-end{justify-self:flex-end}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:.25rem}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-3xl{border-radius:var(--radius-3xl)}.rounded-\\[0\\.5rem\\]{border-radius:.5rem}.rounded-\\[2px\\]{border-radius:2px}.rounded-\\[4px\\]{border-radius:4px}.rounded-\\[inherit\\]{border-radius:inherit}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.rounded-none{border-radius:0}.rounded-sm{border-radius:calc(var(--radius) - 4px)}.rounded-xl{border-radius:calc(var(--radius) + 4px)}.rounded-xs{border-radius:var(--radius-xs)}.rounded-t-lg{border-top-left-radius:var(--radius);border-top-right-radius:var(--radius)}.rounded-tl-sm{border-top-left-radius:calc(var(--radius) - 4px)}.rounded-r{border-top-right-radius:.25rem;border-bottom-right-radius:.25rem}.rounded-r-md{border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-4{border-style:var(--tw-border-style);border-width:4px}.border-\\[1\\.5px\\]{border-style:var(--tw-border-style);border-width:1.5px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-t-2{border-top-style:var(--tw-border-style);border-top-width:2px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.border-\\(--color-border\\){border-color:var(--color-border)}.border-\\[var\\(--iai-primary\\)\\]{border-color:var(--iai-primary)}.border-amber-200{border-color:var(--color-amber-200)}.border-amber-500{border-color:var(--color-amber-500)}.border-blue-200{border-color:var(--color-blue-200)}.border-blue-300{border-color:var(--color-blue-300)}.border-blue-400{border-color:var(--color-blue-400)}.border-blue-500{border-color:var(--color-blue-500)}.border-blue-600{border-color:var(--color-blue-600)}.border-border,.border-border\\/50{border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){.border-border\\/50{border-color:color-mix(in oklab,var(--border)50%,transparent)}}.border-gray-100{border-color:var(--color-gray-100)}.border-gray-200{border-color:var(--color-gray-200)}.border-gray-300{border-color:var(--color-gray-300)}.border-gray-400{border-color:var(--color-gray-400)}.border-green-200{border-color:var(--color-green-200)}.border-green-300{border-color:var(--color-green-300)}.border-green-400{border-color:var(--color-green-400)}.border-green-500{border-color:var(--color-green-500)}.border-green-600{border-color:var(--color-green-600)}.border-input{border-color:var(--input)}.border-muted-foreground\\/25{border-color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.border-muted-foreground\\/25{border-color:color-mix(in oklab,var(--muted-foreground)25%,transparent)}}.border-muted-foreground\\/50{border-color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.border-muted-foreground\\/50{border-color:color-mix(in oklab,var(--muted-foreground)50%,transparent)}}.border-orange-400{border-color:var(--color-orange-400)}.border-primary,.border-primary\\/30{border-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.border-primary\\/30{border-color:color-mix(in oklab,var(--primary)30%,transparent)}}.border-purple-200{border-color:var(--color-purple-200)}.border-purple-400{border-color:var(--color-purple-400)}.border-red-200{border-color:var(--color-red-200)}.border-red-300{border-color:var(--color-red-300)}.border-red-400{border-color:var(--color-red-400)}.border-red-500{border-color:var(--color-red-500)}.border-red-600{border-color:var(--color-red-600)}.border-secondary{border-color:var(--secondary)}.border-sidebar-border{border-color:var(--sidebar-border)}.border-sky-600{border-color:var(--color-sky-600)}.border-transparent{border-color:#0000}.border-white{border-color:var(--color-white)}.border-yellow-200{border-color:var(--color-yellow-200)}.border-yellow-300{border-color:var(--color-yellow-300)}.border-yellow-400{border-color:var(--color-yellow-400)}.border-t-transparent{border-top-color:#0000}.border-l-blue-200{border-left-color:var(--color-blue-200)}.border-l-gray-300{border-left-color:var(--color-gray-300)}.border-l-green-400{border-left-color:var(--color-green-400)}.border-l-transparent{border-left-color:#0000}.bg-\\(--color-bg\\){background-color:var(--color-bg)}.bg-\\[var\\(--iai-primary\\)\\]{background-color:var(--iai-primary)}.bg-accent,.bg-accent\\/50{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.bg-accent\\/50{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.bg-amber-50{background-color:var(--color-amber-50)}.bg-amber-500{background-color:var(--color-amber-500)}.bg-background,.bg-background\\/80{background-color:var(--background)}@supports (color:color-mix(in lab, red, red)){.bg-background\\/80{background-color:color-mix(in oklab,var(--background)80%,transparent)}}.bg-black{background-color:var(--color-black)}.bg-black\\/20{background-color:#0003}@supports (color:color-mix(in lab, red, red)){.bg-black\\/20{background-color:color-mix(in oklab,var(--color-black)20%,transparent)}}.bg-black\\/50{background-color:#00000080}@supports (color:color-mix(in lab, red, red)){.bg-black\\/50{background-color:color-mix(in oklab,var(--color-black)50%,transparent)}}.bg-blue-50{background-color:var(--color-blue-50)}.bg-blue-50\\/50{background-color:#eff6ff80}@supports (color:color-mix(in lab, red, red)){.bg-blue-50\\/50{background-color:color-mix(in oklab,var(--color-blue-50)50%,transparent)}}.bg-blue-100{background-color:var(--color-blue-100)}.bg-blue-200{background-color:var(--color-blue-200)}.bg-blue-400{background-color:var(--color-blue-400)}.bg-blue-500{background-color:var(--color-blue-500)}.bg-blue-600{background-color:var(--color-blue-600)}.bg-border{background-color:var(--border)}.bg-card{background-color:var(--card)}.bg-current{background-color:currentColor}.bg-destructive{background-color:var(--destructive)}.bg-foreground{background-color:var(--foreground)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-gray-100{background-color:var(--color-gray-100)}.bg-gray-200{background-color:var(--color-gray-200)}.bg-gray-300{background-color:var(--color-gray-300)}.bg-gray-600{background-color:var(--color-gray-600)}.bg-gray-900{background-color:var(--color-gray-900)}.bg-green-50{background-color:var(--color-green-50)}.bg-green-50\\/30{background-color:#f0fdf44d}@supports (color:color-mix(in lab, red, red)){.bg-green-50\\/30{background-color:color-mix(in oklab,var(--color-green-50)30%,transparent)}}.bg-green-100{background-color:var(--color-green-100)}.bg-green-400{background-color:var(--color-green-400)}.bg-green-500{background-color:var(--color-green-500)}.bg-green-600{background-color:var(--color-green-600)}.bg-muted,.bg-muted\\/50{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.bg-muted\\/50{background-color:color-mix(in oklab,var(--muted)50%,transparent)}}.bg-orange-50{background-color:var(--color-orange-50)}.bg-orange-100{background-color:var(--color-orange-100)}.bg-popover{background-color:var(--popover)}.bg-primary,.bg-primary\\/5{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.bg-primary\\/5{background-color:color-mix(in oklab,var(--primary)5%,transparent)}}.bg-primary\\/20{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.bg-primary\\/20{background-color:color-mix(in oklab,var(--primary)20%,transparent)}}.bg-purple-50{background-color:var(--color-purple-50)}.bg-purple-100{background-color:var(--color-purple-100)}.bg-purple-400{background-color:var(--color-purple-400)}.bg-purple-600{background-color:var(--color-purple-600)}.bg-red-50{background-color:var(--color-red-50)}.bg-red-100{background-color:var(--color-red-100)}.bg-red-500{background-color:var(--color-red-500)}.bg-red-600{background-color:var(--color-red-600)}.bg-secondary,.bg-secondary\\/50{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){.bg-secondary\\/50{background-color:color-mix(in oklab,var(--secondary)50%,transparent)}}.bg-sidebar{background-color:var(--sidebar)}.bg-sidebar-border{background-color:var(--sidebar-border)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--color-white)}.bg-white\\/10{background-color:#ffffff1a}@supports (color:color-mix(in lab, red, red)){.bg-white\\/10{background-color:color-mix(in oklab,var(--color-white)10%,transparent)}}.bg-white\\/20{background-color:#fff3}@supports (color:color-mix(in lab, red, red)){.bg-white\\/20{background-color:color-mix(in oklab,var(--color-white)20%,transparent)}}.bg-white\\/40{background-color:#fff6}@supports (color:color-mix(in lab, red, red)){.bg-white\\/40{background-color:color-mix(in oklab,var(--color-white)40%,transparent)}}.bg-white\\/95{background-color:#fffffff2}@supports (color:color-mix(in lab, red, red)){.bg-white\\/95{background-color:color-mix(in oklab,var(--color-white)95%,transparent)}}.bg-yellow-50{background-color:var(--color-yellow-50)}.bg-yellow-100{background-color:var(--color-yellow-100)}.bg-yellow-400{background-color:var(--color-yellow-400)}.bg-yellow-500{background-color:var(--color-yellow-500)}.bg-yellow-600{background-color:var(--color-yellow-600)}.bg-linear-to-b{--tw-gradient-position:to bottom}@supports (background-image:linear-gradient(in lab, red, red)){.bg-linear-to-b{--tw-gradient-position:to bottom in oklab}}.bg-linear-to-b{background-image:linear-gradient(var(--tw-gradient-stops))}.bg-linear-to-t{--tw-gradient-position:to top}@supports (background-image:linear-gradient(in lab, red, red)){.bg-linear-to-t{--tw-gradient-position:to top in oklab}}.bg-linear-to-t{background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-b{--tw-gradient-position:to bottom in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-bl{--tw-gradient-position:to bottom left in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-r{--tw-gradient-position:to right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-t{--tw-gradient-position:to top in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.from-blue-50{--tw-gradient-from:var(--color-blue-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-blue-100{--tw-gradient-from:var(--color-blue-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-blue-500{--tw-gradient-from:var(--color-blue-500);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-foreground{--tw-gradient-from:var(--foreground);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-gray-400{--tw-gradient-from:var(--color-gray-400);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-gray-900{--tw-gradient-from:var(--color-gray-900);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-green-50{--tw-gradient-from:var(--color-green-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-primary\\/5{--tw-gradient-from:var(--primary)}@supports (color:color-mix(in lab, red, red)){.from-primary\\/5{--tw-gradient-from:color-mix(in oklab,var(--primary)5%,transparent)}}.from-primary\\/5{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-slate-50{--tw-gradient-from:var(--color-slate-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-slate-800{--tw-gradient-from:var(--color-slate-800);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-transparent{--tw-gradient-from:transparent;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-white{--tw-gradient-from:var(--color-white);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-yellow-50{--tw-gradient-from:var(--color-yellow-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-yellow-400{--tw-gradient-from:var(--color-yellow-400);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-gray-800{--tw-gradient-via:var(--color-gray-800);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.via-white{--tw-gradient-via:var(--color-white);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-black{--tw-gradient-to:var(--color-black);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-blue-50{--tw-gradient-to:var(--color-blue-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-gray-600{--tw-gradient-to:var(--color-gray-600);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-indigo-50{--tw-gradient-to:var(--color-indigo-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-indigo-100{--tw-gradient-to:var(--color-indigo-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-indigo-600{--tw-gradient-to:var(--color-indigo-600);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-orange-50{--tw-gradient-to:var(--color-orange-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-primary\\/20{--tw-gradient-to:var(--primary)}@supports (color:color-mix(in lab, red, red)){.to-primary\\/20{--tw-gradient-to:color-mix(in oklab,var(--primary)20%,transparent)}}.to-primary\\/20{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-purple-600{--tw-gradient-to:var(--color-purple-600);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-slate-900{--tw-gradient-to:var(--color-slate-900);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-transparent{--tw-gradient-to:transparent;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-yellow-600{--tw-gradient-to:var(--color-yellow-600);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.bg-clip-text{-webkit-background-clip:text;background-clip:text}.fill-current{fill:currentColor}.fill-foreground{fill:var(--foreground)}.fill-muted-foreground{fill:var(--muted-foreground)}.fill-primary{fill:var(--primary)}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.\\!p-0{padding:calc(var(--spacing)*0)!important}.p-0{padding:calc(var(--spacing)*0)}.p-0\\!{padding:calc(var(--spacing)*0)!important}.p-1{padding:calc(var(--spacing)*1)}.p-1\\.5{padding:calc(var(--spacing)*1.5)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.p-12{padding:calc(var(--spacing)*12)}.p-px{padding:1px}.px-1{padding-inline:calc(var(--spacing)*1)}.px-1\\.5{padding-inline:calc(var(--spacing)*1.5)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\\.5{padding-inline:calc(var(--spacing)*2.5)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-8{padding-inline:calc(var(--spacing)*8)}.\\!py-0{padding-block:calc(var(--spacing)*0)!important}.py-0{padding-block:calc(var(--spacing)*0)}.py-0\\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-5{padding-block:calc(var(--spacing)*5)}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.py-12{padding-block:calc(var(--spacing)*12)}.py-16{padding-block:calc(var(--spacing)*16)}.\\!pt-3{padding-top:calc(var(--spacing)*3)!important}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-5{padding-top:calc(var(--spacing)*5)}.pt-6{padding-top:calc(var(--spacing)*6)}.pt-8{padding-top:calc(var(--spacing)*8)}.pt-12{padding-top:calc(var(--spacing)*12)}.pt-20{padding-top:calc(var(--spacing)*20)}.pr-1{padding-right:calc(var(--spacing)*1)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-2\\.5{padding-right:calc(var(--spacing)*2.5)}.pr-4{padding-right:calc(var(--spacing)*4)}.pr-8{padding-right:calc(var(--spacing)*8)}.pr-10{padding-right:calc(var(--spacing)*10)}.pr-20{padding-right:calc(var(--spacing)*20)}.pb-0{padding-bottom:calc(var(--spacing)*0)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-3{padding-bottom:calc(var(--spacing)*3)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-6{padding-bottom:calc(var(--spacing)*6)}.pb-8{padding-bottom:calc(var(--spacing)*8)}.pl-2{padding-left:calc(var(--spacing)*2)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-7{padding-left:calc(var(--spacing)*7)}.pl-8{padding-left:calc(var(--spacing)*8)}.pl-9{padding-left:calc(var(--spacing)*9)}.pl-10{padding-left:calc(var(--spacing)*10)}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.align-middle{vertical-align:middle}.font-mono{font-family:var(--font-mono)}.font-sans{font-family:var(--font-sans)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.text-6xl{font-size:var(--text-6xl);line-height:var(--tw-leading,var(--text-6xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\\[0\\.8rem\\]{font-size:.8rem}.text-\\[10px\\]{font-size:10px}.text-\\[10rem\\]{font-size:10rem}.leading-none{--tw-leading:1;line-height:1}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-black{--tw-font-weight:var(--font-weight-black);font-weight:var(--font-weight-black)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-extrabold{--tw-font-weight:var(--font-weight-extrabold);font-weight:var(--font-weight-extrabold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.tracking-wide{--tw-tracking:var(--tracking-wide);letter-spacing:var(--tracking-wide)}.tracking-widest{--tw-tracking:var(--tracking-widest);letter-spacing:var(--tracking-widest)}.text-balance{text-wrap:balance}.break-words{overflow-wrap:break-word}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-wrap{white-space:pre-wrap}.text-\\[var\\(--iai-primary\\)\\]{color:var(--iai-primary)}.text-accent-foreground{color:var(--accent-foreground)}.text-amber-600{color:var(--color-amber-600)}.text-amber-700{color:var(--color-amber-700)}.text-amber-800{color:var(--color-amber-800)}.text-blue-200{color:var(--color-blue-200)}.text-blue-500{color:var(--color-blue-500)}.text-blue-600{color:var(--color-blue-600)}.text-blue-700{color:var(--color-blue-700)}.text-blue-800{color:var(--color-blue-800)}.text-blue-900{color:var(--color-blue-900)}.text-card-foreground{color:var(--card-foreground)}.text-current{color:currentColor}.text-destructive{color:var(--destructive)}.text-foreground,.text-foreground\\/80{color:var(--foreground)}@supports (color:color-mix(in lab, red, red)){.text-foreground\\/80{color:color-mix(in oklab,var(--foreground)80%,transparent)}}.text-gray-100{color:var(--color-gray-100)}.text-gray-200{color:var(--color-gray-200)}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-gray-700{color:var(--color-gray-700)}.text-gray-800{color:var(--color-gray-800)}.text-gray-900{color:var(--color-gray-900)}.text-green-400{color:var(--color-green-400)}.text-green-500{color:var(--color-green-500)}.text-green-600{color:var(--color-green-600)}.text-green-700{color:var(--color-green-700)}.text-green-800{color:var(--color-green-800)}.text-green-900{color:var(--color-green-900)}.text-muted-foreground,.text-muted-foreground\\/70{color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.text-muted-foreground\\/70{color:color-mix(in oklab,var(--muted-foreground)70%,transparent)}}.text-orange-600{color:var(--color-orange-600)}.text-orange-700{color:var(--color-orange-700)}.text-orange-800{color:var(--color-orange-800)}.text-popover-foreground{color:var(--popover-foreground)}.text-primary{color:var(--primary)}.text-primary-foreground{color:var(--primary-foreground)}.text-primary\\/50{color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.text-primary\\/50{color:color-mix(in oklab,var(--primary)50%,transparent)}}.text-purple-600{color:var(--color-purple-600)}.text-purple-700{color:var(--color-purple-700)}.text-purple-800{color:var(--color-purple-800)}.text-red-500{color:var(--color-red-500)}.text-red-600{color:var(--color-red-600)}.text-red-700{color:var(--color-red-700)}.text-red-800{color:var(--color-red-800)}.text-secondary-foreground,.text-secondary-foreground\\/50{color:var(--secondary-foreground)}@supports (color:color-mix(in lab, red, red)){.text-secondary-foreground\\/50{color:color-mix(in oklab,var(--secondary-foreground)50%,transparent)}}.text-sidebar-foreground,.text-sidebar-foreground\\/70{color:var(--sidebar-foreground)}@supports (color:color-mix(in lab, red, red)){.text-sidebar-foreground\\/70{color:color-mix(in oklab,var(--sidebar-foreground)70%,transparent)}}.text-sidebar-primary-foreground{color:var(--sidebar-primary-foreground)}.text-sky-600{color:var(--color-sky-600)}.text-sky-900{color:var(--color-sky-900)}.text-transparent{color:#0000}.text-white{color:var(--color-white)}.text-white\\/90{color:#ffffffe6}@supports (color:color-mix(in lab, red, red)){.text-white\\/90{color:color-mix(in oklab,var(--color-white)90%,transparent)}}.text-yellow-300{color:var(--color-yellow-300)}.text-yellow-500{color:var(--color-yellow-500)}.text-yellow-600{color:var(--color-yellow-600)}.text-yellow-700{color:var(--color-yellow-700)}.text-yellow-800{color:var(--color-yellow-800)}.capitalize{text-transform:capitalize}.uppercase{text-transform:uppercase}.italic{font-style:italic}.tabular-nums{--tw-numeric-spacing:tabular-nums;font-variant-numeric:var(--tw-ordinal,)var(--tw-slashed-zero,)var(--tw-numeric-figure,)var(--tw-numeric-spacing,)var(--tw-numeric-fraction,)}.no-underline\\!{text-decoration-line:none!important}.underline{text-decoration-line:underline}.underline-offset-4{text-underline-offset:4px}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.opacity-0{opacity:0}.opacity-10{opacity:.1}.opacity-30{opacity:.3}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-75{opacity:.75}.opacity-80{opacity:.8}.opacity-90{opacity:.9}.opacity-100{opacity:1}.shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-2xl{--tw-shadow:0 25px 50px -12px var(--tw-shadow-color,#00000040);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\]{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-border)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-0{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-1{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-blue-400{--tw-ring-color:var(--color-blue-400)}.ring-green-200{--tw-ring-color:var(--color-green-200)}.ring-primary{--tw-ring-color:var(--primary)}.ring-red-200{--tw-ring-color:var(--color-red-200)}.ring-ring\\/50{--tw-ring-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){.ring-ring\\/50{--tw-ring-color:color-mix(in oklab,var(--ring)50%,transparent)}}.ring-sidebar-ring{--tw-ring-color:var(--sidebar-ring)}.ring-sky-300{--tw-ring-color:var(--color-sky-300)}.ring-yellow-400{--tw-ring-color:var(--color-yellow-400)}.ring-offset-background{--tw-ring-offset-color:var(--background)}.outline-hidden{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.outline-hidden{outline-offset:2px;outline:2px solid #0000}}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.blur-md{--tw-blur:blur(var(--blur-md));filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.drop-shadow-lg{--tw-drop-shadow-size:drop-shadow(0 4px 4px var(--tw-drop-shadow-color,#00000026));--tw-drop-shadow:drop-shadow(var(--drop-shadow-lg));filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-sm{--tw-backdrop-blur:blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[color\\,box-shadow\\]{transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[left\\,right\\,width\\]{transition-property:left,right,width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[margin\\,opacity\\]{transition-property:margin,opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\,height\\,padding\\]{transition-property:width,height,padding;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\,height\\]{transition-property:width,height;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\]{transition-property:width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-none{transition-property:none}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.duration-500{--tw-duration:.5s;transition-duration:.5s}.duration-1000{--tw-duration:1s;transition-duration:1s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-linear{--tw-ease:linear;transition-timing-function:linear}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.fade-in-0{--tw-enter-opacity:0}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;user-select:none}.zoom-in-95{--tw-enter-scale:.95}.fade-in{--tw-enter-opacity:0}.ring-inset{--tw-ring-inset:inset}.slide-in-from-bottom-2{--tw-enter-translate-y:calc(2*var(--spacing))}.slide-in-from-top-2{--tw-enter-translate-y:calc(2*var(--spacing)*-1)}.group-focus-within\\/menu-item\\:opacity-100:is(:where(.group\\/menu-item):focus-within *){opacity:1}@media (hover:hover){.group-hover\\:bg-blue-200:is(:where(.group):hover *){background-color:var(--color-blue-200)}.group-hover\\:text-blue-600:is(:where(.group):hover *){color:var(--color-blue-600)}.group-hover\\/menu-item\\:opacity-100:is(:where(.group\\/menu-item):hover *){opacity:1}}.group-has-data-\\[collapsible\\=icon\\]\\/sidebar-wrapper\\:h-12:is(:where(.group\\/sidebar-wrapper):has([data-collapsible=icon]) *){height:calc(var(--spacing)*12)}.group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8:is(:where(.group\\/menu-item):has([data-sidebar=menu-action]) *){padding-right:calc(var(--spacing)*8)}.group-data-\\[collapsible\\=icon\\]\\:-mt-8:is(:where(.group)[data-collapsible=icon] *){margin-top:calc(var(--spacing)*-8)}.group-data-\\[collapsible\\=icon\\]\\:hidden:is(:where(.group)[data-collapsible=icon] *){display:none}.group-data-\\[collapsible\\=icon\\]\\:size-8\\!:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}.group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\):is(:where(.group)[data-collapsible=icon] *){width:var(--sidebar-width-icon)}.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + (calc(var(--spacing)*4)))}.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + (calc(var(--spacing)*4)) + 2px)}.group-data-\\[collapsible\\=icon\\]\\:overflow-hidden:is(:where(.group)[data-collapsible=icon] *){overflow:hidden}.group-data-\\[collapsible\\=icon\\]\\:p-0\\!:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*0)!important}.group-data-\\[collapsible\\=icon\\]\\:p-2\\!:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*2)!important}.group-data-\\[collapsible\\=icon\\]\\:opacity-0:is(:where(.group)[data-collapsible=icon] *){opacity:0}.group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){right:calc(var(--sidebar-width)*-1)}.group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){left:calc(var(--sidebar-width)*-1)}.group-data-\\[collapsible\\=offcanvas\\]\\:w-0:is(:where(.group)[data-collapsible=offcanvas] *){width:calc(var(--spacing)*0)}.group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0:is(:where(.group)[data-collapsible=offcanvas] *){--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-data-\\[disabled\\=true\\]\\:pointer-events-none:is(:where(.group)[data-disabled=true] *){pointer-events:none}.group-data-\\[disabled\\=true\\]\\:opacity-50:is(:where(.group)[data-disabled=true] *){opacity:.5}.group-data-\\[side\\=left\\]\\:-right-4:is(:where(.group)[data-side=left] *){right:calc(var(--spacing)*-4)}.group-data-\\[side\\=left\\]\\:border-r:is(:where(.group)[data-side=left] *){border-right-style:var(--tw-border-style);border-right-width:1px}.group-data-\\[side\\=right\\]\\:left-0:is(:where(.group)[data-side=right] *){left:calc(var(--spacing)*0)}.group-data-\\[side\\=right\\]\\:rotate-180:is(:where(.group)[data-side=right] *){rotate:180deg}.group-data-\\[side\\=right\\]\\:border-l:is(:where(.group)[data-side=right] *){border-left-style:var(--tw-border-style);border-left-width:1px}.group-data-\\[state\\=open\\]\\:rotate-180:is(:where(.group)[data-state=open] *){rotate:180deg}.group-data-\\[state\\=open\\]\\/collapsible\\:rotate-90:is(:where(.group\\/collapsible)[data-state=open] *){rotate:90deg}.group-data-\\[variant\\=floating\\]\\:rounded-lg:is(:where(.group)[data-variant=floating] *){border-radius:var(--radius)}.group-data-\\[variant\\=floating\\]\\:border:is(:where(.group)[data-variant=floating] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\\[variant\\=floating\\]\\:border-sidebar-border:is(:where(.group)[data-variant=floating] *){border-color:var(--sidebar-border)}.group-data-\\[variant\\=floating\\]\\:shadow-sm:is(:where(.group)[data-variant=floating] *){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-data-\\[vaul-drawer-direction\\=bottom\\]\\/drawer-content\\:block:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=bottom] *){display:block}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:top-full:is(:where(.group\\/navigation-menu)[data-viewport=false] *){top:100%}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:mt-1\\.5:is(:where(.group\\/navigation-menu)[data-viewport=false] *){margin-top:calc(var(--spacing)*1.5)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:overflow-hidden:is(:where(.group\\/navigation-menu)[data-viewport=false] *){overflow:hidden}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:rounded-md:is(:where(.group\\/navigation-menu)[data-viewport=false] *){border-radius:calc(var(--radius) - 2px)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border:is(:where(.group\\/navigation-menu)[data-viewport=false] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:bg-popover:is(:where(.group\\/navigation-menu)[data-viewport=false] *){background-color:var(--popover)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:text-popover-foreground:is(:where(.group\\/navigation-menu)[data-viewport=false] *){color:var(--popover-foreground)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:shadow:is(:where(.group\\/navigation-menu)[data-viewport=false] *){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:duration-200:is(:where(.group\\/navigation-menu)[data-viewport=false] *){--tw-duration:.2s;transition-duration:.2s}@media (hover:hover){.peer-hover\\/menu-button\\:text-sidebar-accent-foreground:is(:where(.peer\\/menu-button):hover~*){color:var(--sidebar-accent-foreground)}}.peer-disabled\\:cursor-not-allowed:is(:where(.peer):disabled~*){cursor:not-allowed}.peer-disabled\\:opacity-50:is(:where(.peer):disabled~*){opacity:.5}.peer-disabled\\:opacity-70:is(:where(.peer):disabled~*){opacity:.7}.peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground:is(:where(.peer\\/menu-button)[data-active=true]~*){color:var(--sidebar-accent-foreground)}.peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5:is(:where(.peer\\/menu-button)[data-size=default]~*){top:calc(var(--spacing)*1.5)}.peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5:is(:where(.peer\\/menu-button)[data-size=lg]~*){top:calc(var(--spacing)*2.5)}.peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1:is(:where(.peer\\/menu-button)[data-size=sm]~*){top:calc(var(--spacing)*1)}.selection\\:bg-primary ::selection{background-color:var(--primary)}.selection\\:bg-primary::selection{background-color:var(--primary)}.selection\\:text-primary-foreground ::selection{color:var(--primary-foreground)}.selection\\:text-primary-foreground::selection{color:var(--primary-foreground)}.file\\:inline-flex::file-selector-button{display:inline-flex}.file\\:h-7::file-selector-button{height:calc(var(--spacing)*7)}.file\\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\\:bg-transparent::file-selector-button{background-color:#0000}.file\\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\\:font-medium::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\\:text-foreground::file-selector-button{color:var(--foreground)}.placeholder\\:text-muted-foreground::placeholder{color:var(--muted-foreground)}.after\\:absolute:after{content:var(--tw-content);position:absolute}.after\\:-inset-2:after{content:var(--tw-content);inset:calc(var(--spacing)*-2)}.after\\:inset-y-0:after{content:var(--tw-content);inset-block:calc(var(--spacing)*0)}.after\\:left-1\\/2:after{content:var(--tw-content);left:50%}.after\\:w-1:after{content:var(--tw-content);width:calc(var(--spacing)*1)}.after\\:w-\\[2px\\]:after{content:var(--tw-content);width:2px}.after\\:-translate-x-1\\/2:after{content:var(--tw-content);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full:is(:where(.group)[data-collapsible=offcanvas] *):after{content:var(--tw-content);left:100%}.first\\:rounded-l-md:first-child{border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}.first\\:border-l:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}.last\\:rounded-r-md:last-child{border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.last\\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.even\\:border-l:nth-child(2n){border-left-style:var(--tw-border-style);border-left-width:1px}.focus-within\\:relative:focus-within{position:relative}.focus-within\\:z-20:focus-within{z-index:20}@media (hover:hover){.hover\\:scale-\\[1\\.02\\]:hover{scale:1.02}.hover\\:rotate-0:hover{rotate:none}.hover\\:border-gray-300:hover{border-color:var(--color-gray-300)}.hover\\:border-gray-400:hover{border-color:var(--color-gray-400)}.hover\\:border-muted-foreground\\/50:hover{border-color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.hover\\:border-muted-foreground\\/50:hover{border-color:color-mix(in oklab,var(--muted-foreground)50%,transparent)}}.hover\\:bg-\\[var\\(--iai-primary\\)\\]:hover{background-color:var(--iai-primary)}.hover\\:bg-\\[var\\(--iai-secondary\\)\\]:hover{background-color:var(--iai-secondary)}.hover\\:bg-accent:hover{background-color:var(--accent)}.hover\\:bg-black:hover{background-color:var(--color-black)}.hover\\:bg-blue-50:hover{background-color:var(--color-blue-50)}.hover\\:bg-blue-100:hover{background-color:var(--color-blue-100)}.hover\\:bg-blue-700:hover{background-color:var(--color-blue-700)}.hover\\:bg-destructive\\/90:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-destructive\\/90:hover{background-color:color-mix(in oklab,var(--destructive)90%,transparent)}}.hover\\:bg-gray-50:hover{background-color:var(--color-gray-50)}.hover\\:bg-gray-100:hover{background-color:var(--color-gray-100)}.hover\\:bg-gray-800:hover{background-color:var(--color-gray-800)}.hover\\:bg-green-50:hover{background-color:var(--color-green-50)}.hover\\:bg-green-100:hover{background-color:var(--color-green-100)}.hover\\:bg-green-200:hover{background-color:var(--color-green-200)}.hover\\:bg-green-600:hover{background-color:var(--color-green-600)}.hover\\:bg-green-700:hover{background-color:var(--color-green-700)}.hover\\:bg-muted:hover,.hover\\:bg-muted\\/25:hover{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-muted\\/25:hover{background-color:color-mix(in oklab,var(--muted)25%,transparent)}}.hover\\:bg-muted\\/50:hover{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-muted\\/50:hover{background-color:color-mix(in oklab,var(--muted)50%,transparent)}}.hover\\:bg-muted\\/80:hover{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-muted\\/80:hover{background-color:color-mix(in oklab,var(--muted)80%,transparent)}}.hover\\:bg-primary:hover,.hover\\:bg-primary\\/5:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/5:hover{background-color:color-mix(in oklab,var(--primary)5%,transparent)}}.hover\\:bg-primary\\/10:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/10:hover{background-color:color-mix(in oklab,var(--primary)10%,transparent)}}.hover\\:bg-primary\\/90:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/90:hover{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}.hover\\:bg-red-100:hover{background-color:var(--color-red-100)}.hover\\:bg-red-200:hover{background-color:var(--color-red-200)}.hover\\:bg-secondary\\/80:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-secondary\\/80:hover{background-color:color-mix(in oklab,var(--secondary)80%,transparent)}}.hover\\:bg-sidebar-accent:hover{background-color:var(--sidebar-accent)}.hover\\:bg-sky-50:hover{background-color:var(--color-sky-50)}.hover\\:bg-transparent:hover{background-color:#0000}.hover\\:bg-white:hover{background-color:var(--color-white)}.hover\\:bg-white\\/20:hover{background-color:#fff3}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-white\\/20:hover{background-color:color-mix(in oklab,var(--color-white)20%,transparent)}}.hover\\:bg-yellow-500:hover{background-color:var(--color-yellow-500)}.hover\\:bg-yellow-700:hover{background-color:var(--color-yellow-700)}.hover\\:text-\\[var\\(--iai-secondary\\)\\]:hover{color:var(--iai-secondary)}.hover\\:text-accent-foreground:hover{color:var(--accent-foreground)}.hover\\:text-blue-600:hover{color:var(--color-blue-600)}.hover\\:text-blue-800:hover{color:var(--color-blue-800)}.hover\\:text-destructive:hover{color:var(--destructive)}.hover\\:text-foreground:hover{color:var(--foreground)}.hover\\:text-gray-600:hover{color:var(--color-gray-600)}.hover\\:text-gray-700:hover{color:var(--color-gray-700)}.hover\\:text-gray-900:hover{color:var(--color-gray-900)}.hover\\:text-muted-foreground:hover{color:var(--muted-foreground)}.hover\\:text-primary-foreground:hover{color:var(--primary-foreground)}.hover\\:text-red-700:hover{color:var(--color-red-700)}.hover\\:text-sidebar-accent-foreground:hover{color:var(--sidebar-accent-foreground)}.hover\\:text-white:hover{color:var(--color-white)}.hover\\:underline:hover{text-decoration-line:underline}.hover\\:opacity-80:hover{opacity:.8}.hover\\:opacity-100:hover{opacity:1}.hover\\:shadow-2xl:hover{--tw-shadow:0 25px 50px -12px var(--tw-shadow-color,#00000040);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\]:hover{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-accent)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-xl:hover{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:ring-4:hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(4px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar:hover:is(:where(.group)[data-collapsible=offcanvas] *){background-color:var(--sidebar)}.hover\\:after\\:bg-sidebar-border:hover:after{content:var(--tw-content);background-color:var(--sidebar-border)}}.focus\\:z-10:focus{z-index:10}.focus\\:border-\\[var\\(--iai-primary\\)\\]:focus{border-color:var(--iai-primary)}.focus\\:border-red-500:focus{border-color:var(--color-red-500)}.focus\\:border-transparent:focus{border-color:#0000}.focus\\:bg-accent:focus{background-color:var(--accent)}.focus\\:bg-primary:focus{background-color:var(--primary)}.focus\\:text-accent-foreground:focus{color:var(--accent-foreground)}.focus\\:text-primary-foreground:focus{color:var(--primary-foreground)}.focus\\:ring-0:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-1:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-\\[var\\(--iai-primary\\)\\]:focus{--tw-ring-color:var(--iai-primary)}.focus\\:ring-blue-500:focus{--tw-ring-color:var(--color-blue-500)}.focus\\:ring-red-500:focus{--tw-ring-color:var(--color-red-500)}.focus\\:ring-ring:focus{--tw-ring-color:var(--ring)}.focus\\:ring-offset-0:focus{--tw-ring-offset-width:0px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\\:outline-hidden:focus{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus\\:outline-hidden:focus{outline-offset:2px;outline:2px solid #0000}}.focus\\:outline-none:focus{--tw-outline-style:none;outline-style:none}.focus-visible\\:z-10:focus-visible{z-index:10}.focus-visible\\:border-ring:focus-visible{border-color:var(--ring)}.focus-visible\\:ring-1:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-2:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-4:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(4px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-\\[3px\\]:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-\\[var\\(--iai-primary\\)\\]\\/20:focus-visible{--tw-ring-color:var(--iai-primary)}@supports (color:color-mix(in lab, red, red)){.focus-visible\\:ring-\\[var\\(--iai-primary\\)\\]\\/20:focus-visible{--tw-ring-color:color-mix(in oklab,var(--iai-primary)20%,transparent)}}.focus-visible\\:ring-destructive\\/20:focus-visible{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.focus-visible\\:ring-destructive\\/20:focus-visible{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.focus-visible\\:ring-ring:focus-visible,.focus-visible\\:ring-ring\\/50:focus-visible{--tw-ring-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){.focus-visible\\:ring-ring\\/50:focus-visible{--tw-ring-color:color-mix(in oklab,var(--ring)50%,transparent)}}.focus-visible\\:ring-offset-1:focus-visible{--tw-ring-offset-width:1px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\\:outline-hidden:focus-visible{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus-visible\\:outline-hidden:focus-visible{outline-offset:2px;outline:2px solid #0000}}.focus-visible\\:outline-1:focus-visible{outline-style:var(--tw-outline-style);outline-width:1px}.focus-visible\\:outline-none:focus-visible{--tw-outline-style:none;outline-style:none}.active\\:bg-primary\\/90:active{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.active\\:bg-primary\\/90:active{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}.active\\:bg-sidebar-accent:active{background-color:var(--sidebar-accent)}.active\\:text-primary-foreground:active{color:var(--primary-foreground)}.active\\:text-sidebar-accent-foreground:active{color:var(--sidebar-accent-foreground)}.disabled\\:pointer-events-none:disabled{pointer-events:none}.disabled\\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\\:cursor-pointer:disabled{cursor:pointer}.disabled\\:border-none:disabled{--tw-border-style:none;border-style:none}.disabled\\:opacity-50:disabled{opacity:.5}.disabled\\:opacity-100:disabled{opacity:1}:where([data-side=left]) .in-data-\\[side\\=left\\]\\:cursor-w-resize{cursor:w-resize}:where([data-side=right]) .in-data-\\[side\\=right\\]\\:cursor-e-resize{cursor:e-resize}.has-disabled\\:opacity-50:has(:disabled){opacity:.5}.has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\]:has([data-slot=card-action]){grid-template-columns:1fr auto}.has-data-\\[variant\\=inset\\]\\:bg-sidebar:has([data-variant=inset]){background-color:var(--sidebar)}.has-\\[\\>svg\\]\\:grid-cols-\\[calc\\(var\\(--spacing\\)\\*4\\)_1fr\\]:has(>svg){grid-template-columns:calc(var(--spacing)*4)1fr}.has-\\[\\>svg\\]\\:gap-x-3:has(>svg){column-gap:calc(var(--spacing)*3)}.has-\\[\\>svg\\]\\:px-2\\.5:has(>svg){padding-inline:calc(var(--spacing)*2.5)}.has-\\[\\>svg\\]\\:px-3:has(>svg){padding-inline:calc(var(--spacing)*3)}.has-\\[\\>svg\\]\\:px-4:has(>svg){padding-inline:calc(var(--spacing)*4)}.aria-disabled\\:pointer-events-none[aria-disabled=true]{pointer-events:none}.aria-disabled\\:opacity-50[aria-disabled=true]{opacity:.5}.aria-invalid\\:border-destructive[aria-invalid=true]{border-color:var(--destructive)}.aria-invalid\\:ring-destructive\\/20[aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.aria-invalid\\:ring-destructive\\/20[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.aria-selected\\:bg-accent[aria-selected=true]{background-color:var(--accent)}.aria-selected\\:bg-primary[aria-selected=true]{background-color:var(--primary)}.aria-selected\\:text-accent-foreground[aria-selected=true]{color:var(--accent-foreground)}.aria-selected\\:text-muted-foreground[aria-selected=true]{color:var(--muted-foreground)}.aria-selected\\:text-primary-foreground[aria-selected=true]{color:var(--primary-foreground)}.aria-selected\\:opacity-100[aria-selected=true]{opacity:1}.data-\\[active\\=true\\]\\:z-10[data-active=true]{z-index:10}.data-\\[active\\=true\\]\\:border-ring[data-active=true]{border-color:var(--ring)}.data-\\[active\\=true\\]\\:bg-accent\\/50[data-active=true]{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.data-\\[active\\=true\\]\\:bg-accent\\/50[data-active=true]{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.data-\\[active\\=true\\]\\:bg-primary\\/5[data-active=true]{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.data-\\[active\\=true\\]\\:bg-primary\\/5[data-active=true]{background-color:color-mix(in oklab,var(--primary)5%,transparent)}}.data-\\[active\\=true\\]\\:bg-sidebar-accent[data-active=true]{background-color:var(--sidebar-accent)}.data-\\[active\\=true\\]\\:font-medium[data-active=true]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.data-\\[active\\=true\\]\\:text-accent-foreground[data-active=true]{color:var(--accent-foreground)}.data-\\[active\\=true\\]\\:text-sidebar-accent-foreground[data-active=true]{color:var(--sidebar-accent-foreground)}.data-\\[active\\=true\\]\\:ring-\\[3px\\][data-active=true]{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\\[active\\=true\\]\\:ring-ring\\/50[data-active=true]{--tw-ring-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){.data-\\[active\\=true\\]\\:ring-ring\\/50[data-active=true]{--tw-ring-color:color-mix(in oklab,var(--ring)50%,transparent)}}@media (hover:hover){.data-\\[active\\=true\\]\\:hover\\:bg-accent[data-active=true]:hover{background-color:var(--accent)}}.data-\\[active\\=true\\]\\:focus\\:bg-accent[data-active=true]:focus{background-color:var(--accent)}.data-\\[active\\=true\\]\\:aria-invalid\\:border-destructive[data-active=true][aria-invalid=true]{border-color:var(--destructive)}.data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/20[data-active=true][aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/20[data-active=true][aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{pointer-events:none}.data-\\[disabled\\]\\:opacity-50[data-disabled]{opacity:.5}.data-\\[disabled\\=true\\]\\:pointer-events-none[data-disabled=true]{pointer-events:none}.data-\\[disabled\\=true\\]\\:opacity-50[data-disabled=true]{opacity:.5}.data-\\[error\\=true\\]\\:text-destructive[data-error=true]{color:var(--destructive)}.data-\\[inset\\]\\:pl-8[data-inset]{padding-left:calc(var(--spacing)*8)}.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=from-end]{--tw-enter-translate-x:calc(52*var(--spacing))}.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=from-start]{--tw-enter-translate-x:calc(52*var(--spacing)*-1)}.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=to-end]{--tw-exit-translate-x:calc(52*var(--spacing))}.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=to-start]{--tw-exit-translate-x:calc(52*var(--spacing)*-1)}.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=from-]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=from-]{--tw-enter-opacity:0}.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=to-]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=to-]{--tw-exit-opacity:0}.data-\\[orientation\\=horizontal\\]\\:h-1\\.5[data-orientation=horizontal]{height:calc(var(--spacing)*1.5)}.data-\\[orientation\\=horizontal\\]\\:h-full[data-orientation=horizontal]{height:100%}.data-\\[orientation\\=horizontal\\]\\:w-full[data-orientation=horizontal]{width:100%}.data-\\[orientation\\=vertical\\]\\:h-4[data-orientation=vertical]{height:calc(var(--spacing)*4)}.data-\\[orientation\\=vertical\\]\\:h-full[data-orientation=vertical]{height:100%}.data-\\[orientation\\=vertical\\]\\:min-h-44[data-orientation=vertical]{min-height:calc(var(--spacing)*44)}.data-\\[orientation\\=vertical\\]\\:w-1\\.5[data-orientation=vertical]{width:calc(var(--spacing)*1.5)}.data-\\[orientation\\=vertical\\]\\:w-auto[data-orientation=vertical]{width:auto}.data-\\[orientation\\=vertical\\]\\:w-full[data-orientation=vertical]{width:100%}.data-\\[orientation\\=vertical\\]\\:flex-col[data-orientation=vertical]{flex-direction:column}.data-\\[panel-group-direction\\=vertical\\]\\:h-px[data-panel-group-direction=vertical]{height:1px}.data-\\[panel-group-direction\\=vertical\\]\\:w-full[data-panel-group-direction=vertical]{width:100%}.data-\\[panel-group-direction\\=vertical\\]\\:flex-col[data-panel-group-direction=vertical]{flex-direction:column}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);left:calc(var(--spacing)*0)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1[data-panel-group-direction=vertical]:after{content:var(--tw-content);height:calc(var(--spacing)*1)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full[data-panel-group-direction=vertical]:after{content:var(--tw-content);width:100%}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[placeholder\\]\\:text-muted-foreground[data-placeholder]{color:var(--muted-foreground)}.data-\\[selected\\=true\\]\\:bg-accent[data-selected=true]{background-color:var(--accent)}.data-\\[selected\\=true\\]\\:text-accent-foreground[data-selected=true]{color:var(--accent-foreground)}.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=bottom]{--tw-translate-y:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y:calc(2*var(--spacing)*-1)}.data-\\[side\\=left\\]\\:-translate-x-1[data-side=left]{--tw-translate-x:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=left]{--tw-enter-translate-x:calc(2*var(--spacing))}.data-\\[side\\=right\\]\\:translate-x-1[data-side=right]{--tw-translate-x:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=right]{--tw-enter-translate-x:calc(2*var(--spacing)*-1)}.data-\\[side\\=top\\]\\:-translate-y-1[data-side=top]{--tw-translate-y:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y:calc(2*var(--spacing))}.data-\\[size\\=default\\]\\:h-9[data-size=default]{height:calc(var(--spacing)*9)}.data-\\[size\\=sm\\]\\:h-8[data-size=sm]{height:calc(var(--spacing)*8)}:is(.\\*\\:data-\\[slot\\=alert-description\\]\\:text-destructive\\/90>*)[data-slot=alert-description]{color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){:is(.\\*\\:data-\\[slot\\=alert-description\\]\\:text-destructive\\/90>*)[data-slot=alert-description]{color:color-mix(in oklab,var(--destructive)90%,transparent)}}:is(.\\*\\:data-\\[slot\\=card\\]\\:bg-gradient-to-t>*)[data-slot=card]{--tw-gradient-position:to top in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}:is(.\\*\\:data-\\[slot\\=card\\]\\:from-primary\\/5>*)[data-slot=card]{--tw-gradient-from:var(--primary)}@supports (color:color-mix(in lab, red, red)){:is(.\\*\\:data-\\[slot\\=card\\]\\:from-primary\\/5>*)[data-slot=card]{--tw-gradient-from:color-mix(in oklab,var(--primary)5%,transparent)}}:is(.\\*\\:data-\\[slot\\=card\\]\\:from-primary\\/5>*)[data-slot=card]{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}:is(.\\*\\:data-\\[slot\\=card\\]\\:to-card>*)[data-slot=card]{--tw-gradient-to:var(--card);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}:is(.\\*\\:data-\\[slot\\=card\\]\\:shadow-xs>*)[data-slot=card]{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.\\*\\*\\:data-\\[slot\\=command-input-wrapper\\]\\:h-12 *)[data-slot=command-input-wrapper]{height:calc(var(--spacing)*12)}:is(.\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:ring-0 *)[data-slot=navigation-menu-link]:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:outline-none *)[data-slot=navigation-menu-link]:focus{--tw-outline-style:none;outline-style:none}:is(.\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1>*)[data-slot=select-value]{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}:is(.\\*\\:data-\\[slot\\=select-value\\]\\:flex>*)[data-slot=select-value]{display:flex}:is(.\\*\\:data-\\[slot\\=select-value\\]\\:w-12>*)[data-slot=select-value]{width:calc(var(--spacing)*12)}:is(.\\*\\:data-\\[slot\\=select-value\\]\\:items-center>*)[data-slot=select-value]{align-items:center}:is(.\\*\\:data-\\[slot\\=select-value\\]\\:gap-2>*)[data-slot=select-value]{gap:calc(var(--spacing)*2)}.data-\\[state\\=active\\]\\:bg-background[data-state=active]{background-color:var(--background)}.data-\\[state\\=active\\]\\:text-foreground[data-state=active]{color:var(--foreground)}.data-\\[state\\=active\\]\\:shadow-sm[data-state=active]{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\][data-state=checked]{--tw-translate-x:calc(100% - 2px);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=checked\\]\\:border-\\[var\\(--iai-primary\\)\\][data-state=checked]{border-color:var(--iai-primary)}.data-\\[state\\=checked\\]\\:border-primary[data-state=checked]{border-color:var(--primary)}.data-\\[state\\=checked\\]\\:bg-\\[var\\(--iai-primary\\)\\][data-state=checked]{background-color:var(--iai-primary)}.data-\\[state\\=checked\\]\\:bg-primary[data-state=checked]{background-color:var(--primary)}.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=checked]{color:var(--primary-foreground)}.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=closed]{animation:accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))var(--tw-ease,ease-out)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[state\\=closed\\]\\:animate-out[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[state\\=closed\\]\\:duration-300[data-state=closed]{--tw-duration:.3s;transition-duration:.3s}.data-\\[state\\=closed\\]\\:fade-out-0[data-state=closed]{--tw-exit-opacity:0}.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=closed]{--tw-exit-scale:.95}.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=closed]{--tw-exit-translate-y:100%}.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=closed]{--tw-exit-translate-x:-100%}.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=closed]{--tw-exit-translate-x:100%}.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=closed]{--tw-exit-translate-y:-100%}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:animate-out:is(:where(.group\\/navigation-menu)[data-viewport=false] *)[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:fade-out-0:is(:where(.group\\/navigation-menu)[data-viewport=false] *)[data-state=closed]{--tw-exit-opacity:0}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:zoom-out-95:is(:where(.group\\/navigation-menu)[data-viewport=false] *)[data-state=closed]{--tw-exit-scale:.95}.data-\\[state\\=hidden\\]\\:animate-out[data-state=hidden]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[state\\=hidden\\]\\:fade-out[data-state=hidden]{--tw-exit-opacity:0}.data-\\[state\\=on\\]\\:bg-accent[data-state=on]{background-color:var(--accent)}.data-\\[state\\=on\\]\\:text-accent-foreground[data-state=on]{color:var(--accent-foreground)}.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=open]{animation:accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))var(--tw-ease,ease-out)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[state\\=open\\]\\:animate-in[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[state\\=open\\]\\:bg-accent[data-state=open],.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=open]{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=open]{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.data-\\[state\\=open\\]\\:bg-secondary[data-state=open]{background-color:var(--secondary)}.data-\\[state\\=open\\]\\:bg-sidebar-accent[data-state=open]{background-color:var(--sidebar-accent)}.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=open]{color:var(--accent-foreground)}.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=open]{color:var(--muted-foreground)}.data-\\[state\\=open\\]\\:text-sidebar-accent-foreground[data-state=open]{color:var(--sidebar-accent-foreground)}.data-\\[state\\=open\\]\\:opacity-100[data-state=open]{opacity:1}.data-\\[state\\=open\\]\\:duration-500[data-state=open]{--tw-duration:.5s;transition-duration:.5s}.data-\\[state\\=open\\]\\:fade-in-0[data-state=open]{--tw-enter-opacity:0}.data-\\[state\\=open\\]\\:zoom-in-90[data-state=open]{--tw-enter-scale:.9}.data-\\[state\\=open\\]\\:zoom-in-95[data-state=open]{--tw-enter-scale:.95}.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=open]{--tw-enter-translate-y:100%}.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=open]{--tw-enter-translate-x:-100%}.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=open]{--tw-enter-translate-x:100%}.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=open]{--tw-enter-translate-y:-100%}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:animate-in:is(:where(.group\\/navigation-menu)[data-viewport=false] *)[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:fade-in-0:is(:where(.group\\/navigation-menu)[data-viewport=false] *)[data-state=open]{--tw-enter-opacity:0}.group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:zoom-in-95:is(:where(.group\\/navigation-menu)[data-viewport=false] *)[data-state=open]{--tw-enter-scale:.95}@media (hover:hover){.data-\\[state\\=open\\]\\:hover\\:bg-accent[data-state=open]:hover{background-color:var(--accent)}.data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent[data-state=open]:hover{background-color:var(--sidebar-accent)}.data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground[data-state=open]:hover{color:var(--sidebar-accent-foreground)}}.data-\\[state\\=open\\]\\:focus\\:bg-accent[data-state=open]:focus{background-color:var(--accent)}.data-\\[state\\=selected\\]\\:bg-muted[data-state=selected]{background-color:var(--muted)}.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=unchecked]{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=unchecked\\]\\:bg-input[data-state=unchecked]{background-color:var(--input)}.data-\\[state\\=visible\\]\\:animate-in[data-state=visible]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\\[state\\=visible\\]\\:fade-in[data-state=visible]{--tw-enter-opacity:0}.data-\\[variant\\=destructive\\]\\:text-destructive[data-variant=destructive]{color:var(--destructive)}.data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10[data-variant=destructive]:focus{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10[data-variant=destructive]:focus{background-color:color-mix(in oklab,var(--destructive)10%,transparent)}}.data-\\[variant\\=destructive\\]\\:focus\\:text-destructive[data-variant=destructive]:focus{color:var(--destructive)}.data-\\[variant\\=outline\\]\\:border-l-0[data-variant=outline]{border-left-style:var(--tw-border-style);border-left-width:0}.data-\\[variant\\=outline\\]\\:shadow-xs[data-variant=outline]{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\\[variant\\=outline\\]\\:first\\:border-l[data-variant=outline]:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}.data-\\[vaul-drawer-direction\\=bottom\\]\\:inset-x-0[data-vaul-drawer-direction=bottom]{inset-inline:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=bottom\\]\\:bottom-0[data-vaul-drawer-direction=bottom]{bottom:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=bottom\\]\\:mt-24[data-vaul-drawer-direction=bottom]{margin-top:calc(var(--spacing)*24)}.data-\\[vaul-drawer-direction\\=bottom\\]\\:max-h-\\[80vh\\][data-vaul-drawer-direction=bottom]{max-height:80vh}.data-\\[vaul-drawer-direction\\=bottom\\]\\:rounded-t-lg[data-vaul-drawer-direction=bottom]{border-top-left-radius:var(--radius);border-top-right-radius:var(--radius)}.data-\\[vaul-drawer-direction\\=bottom\\]\\:border-t[data-vaul-drawer-direction=bottom]{border-top-style:var(--tw-border-style);border-top-width:1px}.data-\\[vaul-drawer-direction\\=left\\]\\:inset-y-0[data-vaul-drawer-direction=left]{inset-block:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=left\\]\\:left-0[data-vaul-drawer-direction=left]{left:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=left\\]\\:w-3\\/4[data-vaul-drawer-direction=left]{width:75%}.data-\\[vaul-drawer-direction\\=left\\]\\:border-r[data-vaul-drawer-direction=left]{border-right-style:var(--tw-border-style);border-right-width:1px}.data-\\[vaul-drawer-direction\\=right\\]\\:inset-y-0[data-vaul-drawer-direction=right]{inset-block:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=right\\]\\:right-0[data-vaul-drawer-direction=right]{right:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=right\\]\\:w-3\\/4[data-vaul-drawer-direction=right]{width:75%}.data-\\[vaul-drawer-direction\\=right\\]\\:border-l[data-vaul-drawer-direction=right]{border-left-style:var(--tw-border-style);border-left-width:1px}.data-\\[vaul-drawer-direction\\=top\\]\\:inset-x-0[data-vaul-drawer-direction=top]{inset-inline:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=top\\]\\:top-0[data-vaul-drawer-direction=top]{top:calc(var(--spacing)*0)}.data-\\[vaul-drawer-direction\\=top\\]\\:mb-24[data-vaul-drawer-direction=top]{margin-bottom:calc(var(--spacing)*24)}.data-\\[vaul-drawer-direction\\=top\\]\\:max-h-\\[80vh\\][data-vaul-drawer-direction=top]{max-height:80vh}.data-\\[vaul-drawer-direction\\=top\\]\\:rounded-b-lg[data-vaul-drawer-direction=top]{border-bottom-right-radius:var(--radius);border-bottom-left-radius:var(--radius)}.data-\\[vaul-drawer-direction\\=top\\]\\:border-b[data-vaul-drawer-direction=top]{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}@media (min-width:40rem){.sm\\:mb-4{margin-bottom:calc(var(--spacing)*4)}.sm\\:mb-8{margin-bottom:calc(var(--spacing)*8)}.sm\\:block{display:block}.sm\\:flex{display:flex}.sm\\:hidden{display:none}.sm\\:h-10{height:calc(var(--spacing)*10)}.sm\\:h-\\[200px\\]{height:200px}.sm\\:w-\\[200px\\]{width:200px}.sm\\:max-w-2xl{max-width:var(--container-2xl)}.sm\\:max-w-4xl{max-width:var(--container-4xl)}.sm\\:max-w-\\[425px\\]{max-width:425px}.sm\\:max-w-lg{max-width:var(--container-lg)}.sm\\:max-w-md{max-width:var(--container-md)}.sm\\:max-w-sm{max-width:var(--container-sm)}.sm\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\:flex-row{flex-direction:row}.sm\\:justify-end{justify-content:flex-end}.sm\\:gap-2\\.5{gap:calc(var(--spacing)*2.5)}.sm\\:gap-6{gap:calc(var(--spacing)*6)}.sm\\:gap-8{gap:calc(var(--spacing)*8)}:where(.sm\\:space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}.sm\\:border-t-0{border-top-style:var(--tw-border-style);border-top-width:0}.sm\\:border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.sm\\:p-6{padding:calc(var(--spacing)*6)}.sm\\:p-8{padding:calc(var(--spacing)*8)}.sm\\:px-5{padding-inline:calc(var(--spacing)*5)}.sm\\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\\:px-8{padding-inline:calc(var(--spacing)*8)}.sm\\:py-6{padding-block:calc(var(--spacing)*6)}.sm\\:pt-6{padding-top:calc(var(--spacing)*6)}.sm\\:pr-2\\.5{padding-right:calc(var(--spacing)*2.5)}.sm\\:pr-12{padding-right:calc(var(--spacing)*12)}.sm\\:pb-6{padding-bottom:calc(var(--spacing)*6)}.sm\\:pl-2\\.5{padding-left:calc(var(--spacing)*2.5)}.sm\\:text-left{text-align:left}.sm\\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.sm\\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.sm\\:text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.sm\\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.data-\\[vaul-drawer-direction\\=left\\]\\:sm\\:max-w-sm[data-vaul-drawer-direction=left],.data-\\[vaul-drawer-direction\\=right\\]\\:sm\\:max-w-sm[data-vaul-drawer-direction=right]{max-width:var(--container-sm)}}@media (min-width:48rem){.md\\:absolute{position:absolute}.md\\:col-span-2{grid-column:span 2/span 2}.md\\:col-span-3{grid-column:span 3/span 3}.md\\:block{display:block}.md\\:flex{display:flex}.md\\:grid{display:grid}.md\\:hidden{display:none}.md\\:inline-block{display:inline-block}.md\\:w-40{width:calc(var(--spacing)*40)}.md\\:w-96{width:calc(var(--spacing)*96)}.md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\]{width:var(--radix-navigation-menu-viewport-width)}.md\\:w-auto{width:auto}.md\\:max-w-lg{max-width:var(--container-lg)}.md\\:flex-1{flex:1}.md\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.md\\:border-t-4{border-top-style:var(--tw-border-style);border-top-width:4px}.md\\:border-l-0{border-left-style:var(--tw-border-style);border-left-width:0}.md\\:px-0{padding-inline:calc(var(--spacing)*0)}.md\\:px-6{padding-inline:calc(var(--spacing)*6)}.md\\:pt-4{padding-top:calc(var(--spacing)*4)}.md\\:pb-0{padding-bottom:calc(var(--spacing)*0)}.md\\:pl-0{padding-left:calc(var(--spacing)*0)}.md\\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.md\\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.md\\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.md\\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.md\\:opacity-0{opacity:0}.md\\:peer-data-\\[variant\\=inset\\]\\:m-2:is(:where(.peer)[data-variant=inset]~*){margin:calc(var(--spacing)*2)}.md\\:peer-data-\\[variant\\=inset\\]\\:ml-0:is(:where(.peer)[data-variant=inset]~*){margin-left:calc(var(--spacing)*0)}.md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl:is(:where(.peer)[data-variant=inset]~*){border-radius:calc(var(--radius) + 4px)}.md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm:is(:where(.peer)[data-variant=inset]~*){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2:is(:where(.peer)[data-variant=inset]~*):is(:where(.peer)[data-state=collapsed]~*){margin-left:calc(var(--spacing)*2)}.md\\:after\\:hidden:after{content:var(--tw-content);display:none}}@media (min-width:64rem){.lg\\:col-span-1{grid-column:span 1/span 1}.lg\\:col-span-2{grid-column:span 2/span 2}.lg\\:col-span-3{grid-column:span 3/span 3}.lg\\:mx-0{margin-inline:calc(var(--spacing)*0)}.lg\\:mb-16{margin-bottom:calc(var(--spacing)*16)}.lg\\:block{display:block}.lg\\:flex{display:flex}.lg\\:hidden{display:none}.lg\\:h-full{height:100%}.lg\\:w-1\\/2{width:50%}.lg\\:w-56{width:calc(var(--spacing)*56)}.lg\\:w-64{width:calc(var(--spacing)*64)}.lg\\:max-w-2xl{max-width:var(--container-2xl)}.lg\\:max-w-xl{max-width:var(--container-xl)}.lg\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\\:grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.lg\\:grid-cols-7{grid-template-columns:repeat(7,minmax(0,1fr))}.lg\\:justify-end{justify-content:flex-end}.lg\\:justify-start{justify-content:flex-start}.lg\\:gap-8{gap:calc(var(--spacing)*8)}.lg\\:p-8{padding:calc(var(--spacing)*8)}.lg\\:p-12{padding:calc(var(--spacing)*12)}.lg\\:px-6{padding-inline:calc(var(--spacing)*6)}.lg\\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\\:py-24{padding-block:calc(var(--spacing)*24)}.lg\\:text-left{text-align:left}.lg\\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.lg\\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.lg\\:text-6xl{font-size:var(--text-6xl);line-height:var(--tw-leading,var(--text-6xl--line-height))}.lg\\:text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.lg\\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}}@media (min-width:80rem){.xl\\:col-span-1{grid-column:span 1/span 1}.xl\\:col-span-3{grid-column:span 3/span 3}.xl\\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}}@container card (min-width:250px){.\\@\\[250px\\]\\/card\\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}}@container card (min-width:540px){.\\@\\[540px\\]\\/card\\:block{display:block}.\\@\\[540px\\]\\/card\\:hidden{display:none}}@container main (min-width:36rem){.\\@xl\\/main\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}}@container main (min-width:64rem){.\\@5xl\\/main\\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}}.dark\\:border-input:is(.dark *){border-color:var(--input)}.dark\\:bg-destructive\\/60:is(.dark *){background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\\:bg-destructive\\/60:is(.dark *){background-color:color-mix(in oklab,var(--destructive)60%,transparent)}}.dark\\:bg-input\\/30:is(.dark *){background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\\:bg-input\\/30:is(.dark *){background-color:color-mix(in oklab,var(--input)30%,transparent)}}.dark\\:text-foreground:is(.dark *){color:var(--foreground)}@media (hover:hover){.dark\\:hover\\:bg-accent\\/50:is(.dark *):hover{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.dark\\:hover\\:bg-accent\\/50:is(.dark *):hover{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.dark\\:hover\\:bg-input\\/50:is(.dark *):hover{background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\\:hover\\:bg-input\\/50:is(.dark *):hover{background-color:color-mix(in oklab,var(--input)50%,transparent)}}}.dark\\:focus-visible\\:ring-destructive\\/40:is(.dark *):focus-visible{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\\:focus-visible\\:ring-destructive\\/40:is(.dark *):focus-visible{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}.dark\\:aria-invalid\\:ring-destructive\\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\\:aria-invalid\\:ring-destructive\\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}.dark\\:data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/40:is(.dark *)[data-active=true][aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\\:data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/40:is(.dark *)[data-active=true][aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}:is(.dark\\:\\*\\:data-\\[slot\\=card\\]\\:bg-card:is(.dark *)>*)[data-slot=card]{background-color:var(--card)}.dark\\:data-\\[state\\=checked\\]\\:bg-primary:is(.dark *)[data-state=checked]{background-color:var(--primary)}.dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground:is(.dark *)[data-state=checked]{background-color:var(--primary-foreground)}.dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground:is(.dark *)[data-state=unchecked]{background-color:var(--foreground)}.dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80:is(.dark *)[data-state=unchecked]{background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80:is(.dark *)[data-state=unchecked]{background-color:color-mix(in oklab,var(--input)80%,transparent)}}.dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20:is(.dark *)[data-variant=destructive]:focus{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20:is(.dark *)[data-variant=destructive]:focus{background-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text{fill:var(--muted-foreground)}.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke=\\#ccc]{stroke:var(--border)}@supports (color:color-mix(in lab, red, red)){.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke=\\#ccc]{stroke:color-mix(in oklab,var(--border)50%,transparent)}}.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor{stroke:var(--border)}.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke=\\#fff]{stroke:#0000}.\\[\\&_\\.recharts-layer\\]\\:outline-hidden .recharts-layer{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.\\[\\&_\\.recharts-layer\\]\\:outline-hidden .recharts-layer{outline-offset:2px;outline:2px solid #0000}}.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke=\\#ccc]{stroke:var(--border)}.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector,.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{fill:var(--muted)}.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke=\\#ccc]{stroke:var(--border)}.\\[\\&_\\.recharts-sector\\]\\:outline-hidden .recharts-sector{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.\\[\\&_\\.recharts-sector\\]\\:outline-hidden .recharts-sector{outline-offset:2px;outline:2px solid #0000}}.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke=\\#fff]{stroke:#0000}.\\[\\&_\\.recharts-surface\\]\\:outline-hidden .recharts-surface{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.\\[\\&_\\.recharts-surface\\]\\:outline-hidden .recharts-surface{outline-offset:2px;outline:2px solid #0000}}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 [cmdk-group-heading]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 [cmdk-group-heading]{padding-block:calc(var(--spacing)*1.5)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs [cmdk-group-heading]{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium [cmdk-group-heading]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground [cmdk-group-heading]{color:var(--muted-foreground)}.\\[\\&_\\[cmdk-group\\]\\]\\:px-2 [cmdk-group]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group]{padding-top:calc(var(--spacing)*0)}.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 [cmdk-input-wrapper] svg{height:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 [cmdk-input-wrapper] svg{width:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-input\\]\\]\\:h-12 [cmdk-input]{height:calc(var(--spacing)*12)}.\\[\\&_\\[cmdk-item\\]\\]\\:px-2 [cmdk-item]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-item\\]\\]\\:py-3 [cmdk-item]{padding-block:calc(var(--spacing)*3)}.\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 [cmdk-item] svg{height:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 [cmdk-item] svg{width:calc(var(--spacing)*5)}.\\[\\&_p\\]\\:leading-relaxed p{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.\\[\\&_svg\\]\\:pointer-events-none svg{pointer-events:none}.\\[\\&_svg\\]\\:invisible svg{visibility:hidden}.\\[\\&_svg\\]\\:size-4 svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&_svg\\]\\:shrink-0 svg{flex-shrink:0}.\\[\\&_svg\\]\\:text-muted-foreground svg{color:var(--muted-foreground)}.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground svg:not([class*=text-]){color:var(--muted-foreground)}.focus\\:\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-accent-foreground:focus svg:not([class*=text-]){color:var(--accent-foreground)}.\\[\\&_tr\\]\\:border-b tr{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{border-style:var(--tw-border-style);border-width:0}.\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md:has(>.day-range-end){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md:has(>.day-range-start){border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md:has([aria-selected]){border-radius:calc(var(--radius) - 2px)}.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]){background-color:var(--accent)}.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:first-child:has([aria-selected]){border-top-left-radius:calc(var(--radius) - 2px);border-bottom-left-radius:calc(var(--radius) - 2px)}.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:last-child:has([aria-selected]),.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end){border-top-right-radius:calc(var(--radius) - 2px);border-bottom-right-radius:calc(var(--radius) - 2px)}.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){padding-right:calc(var(--spacing)*0)}.\\[\\.border-b\\]\\:pb-6.border-b{padding-bottom:calc(var(--spacing)*6)}.\\[\\.border-t\\]\\:pt-6.border-t{padding-top:calc(var(--spacing)*6)}:is(.\\*\\:\\[span\\]\\:last\\:flex>*):is(span):last-child{display:flex}:is(.\\*\\:\\[span\\]\\:last\\:items-center>*):is(span):last-child{align-items:center}:is(.\\*\\:\\[span\\]\\:last\\:gap-2>*):is(span):last-child{gap:calc(var(--spacing)*2)}:is(.data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive[data-variant=destructive]>*):is(svg){color:var(--destructive)!important}.\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\]>[role=checkbox]{--tw-translate-y:2px;translate:var(--tw-translate-x)var(--tw-translate-y)}.\\[\\&\\>button\\]\\:hidden>button{display:none}.\\[\\&\\>span\\:first-child\\]\\:right-2>span:first-child{right:calc(var(--spacing)*2)}.\\[\\&\\>span\\:first-child\\]\\:left-auto>span:first-child{left:auto}.\\[\\&\\>span\\:last-child\\]\\:truncate>span:last-child{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.\\[\\&\\>svg\\]\\:pointer-events-none>svg{pointer-events:none}.\\[\\&\\>svg\\]\\:size-3>svg{width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:size-3\\.5>svg{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.\\[\\&\\>svg\\]\\:size-4>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:h-2\\.5>svg{height:calc(var(--spacing)*2.5)}.\\[\\&\\>svg\\]\\:h-3>svg{height:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:w-2\\.5>svg{width:calc(var(--spacing)*2.5)}.\\[\\&\\>svg\\]\\:w-3>svg{width:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:shrink-0>svg{flex-shrink:0}.\\[\\&\\>svg\\]\\:translate-y-0\\.5>svg{--tw-translate-y:calc(var(--spacing)*.5);translate:var(--tw-translate-x)var(--tw-translate-y)}.\\[\\&\\>svg\\]\\:text-current>svg{color:currentColor}.\\[\\&\\>svg\\]\\:text-muted-foreground>svg{color:var(--muted-foreground)}.\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground>svg{color:var(--sidebar-accent-foreground)}.\\[\\&\\>tr\\]\\:last\\:border-b-0>tr:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90[data-panel-group-direction=vertical]>div{rotate:90deg}.\\[\\&\\[data-size\\]\\]\\:h-8[data-size]{height:calc(var(--spacing)*8)}.\\[\\&\\[data-state\\=closed\\]\\>button\\]\\:hidden[data-state=closed]>button,.\\[\\&\\[data-state\\=open\\]\\>\\.alert\\]\\:hidden[data-state=open]>.alert{display:none}.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg{rotate:180deg}[data-side=left][data-collapsible=offcanvas] .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2{right:calc(var(--spacing)*-2)}[data-side=left][data-state=collapsed] .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize{cursor:e-resize}[data-side=right][data-collapsible=offcanvas] .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2{left:calc(var(--spacing)*-2)}[data-side=right][data-state=collapsed] .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize{cursor:w-resize}@media (hover:hover){a.\\[a\\&\\]\\:hover\\:bg-accent:hover{background-color:var(--accent)}a.\\[a\\&\\]\\:hover\\:bg-destructive\\/90:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){a.\\[a\\&\\]\\:hover\\:bg-destructive\\/90:hover{background-color:color-mix(in oklab,var(--destructive)90%,transparent)}}a.\\[a\\&\\]\\:hover\\:bg-primary\\/90:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){a.\\[a\\&\\]\\:hover\\:bg-primary\\/90:hover{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}a.\\[a\\&\\]\\:hover\\:bg-secondary\\/90:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){a.\\[a\\&\\]\\:hover\\:bg-secondary\\/90:hover{background-color:color-mix(in oklab,var(--secondary)90%,transparent)}}a.\\[a\\&\\]\\:hover\\:text-accent-foreground:hover{color:var(--accent-foreground)}}}@property --tw-animation-delay{syntax:\"*\";inherits:false;initial-value:0s}@property --tw-animation-direction{syntax:\"*\";inherits:false;initial-value:normal}@property --tw-animation-duration{syntax:\"*\";inherits:false}@property --tw-animation-fill-mode{syntax:\"*\";inherits:false;initial-value:none}@property --tw-animation-iteration-count{syntax:\"*\";inherits:false;initial-value:1}@property --tw-enter-opacity{syntax:\"*\";inherits:false;initial-value:1}@property --tw-enter-rotate{syntax:\"*\";inherits:false;initial-value:0}@property --tw-enter-scale{syntax:\"*\";inherits:false;initial-value:1}@property --tw-enter-translate-x{syntax:\"*\";inherits:false;initial-value:0}@property --tw-enter-translate-y{syntax:\"*\";inherits:false;initial-value:0}@property --tw-exit-opacity{syntax:\"*\";inherits:false;initial-value:1}@property --tw-exit-rotate{syntax:\"*\";inherits:false;initial-value:0}@property --tw-exit-scale{syntax:\"*\";inherits:false;initial-value:1}@property --tw-exit-translate-x{syntax:\"*\";inherits:false;initial-value:0}@property --tw-exit-translate-y{syntax:\"*\";inherits:false;initial-value:0}body{overscroll-behavior:none;background-color:#0000}:root{--font-sans:var(--font-inter);--header-height:calc(var(--spacing)*12 + 1px)}@media (min-width:1024px){.theme-scaled{--radius:.6rem;--text-lg:1.05rem;--text-base:.85rem;--text-sm:.8rem;--spacing:.222222rem}}.theme-scaled [data-slot=card]{--spacing:.16rem}.theme-scaled [data-slot=select-trigger],.theme-scaled [data-slot=toggle-group-item]{--spacing:.222222rem}.theme-default,.theme-default-scaled{--primary:var(--color-neutral-600);--primary-foreground:var(--color-neutral-50)}:is(.theme-default,.theme-default-scaled):is(.dark *){--primary:var(--color-neutral-500);--primary-foreground:var(--color-neutral-50)}.theme-blue,.theme-blue-scaled{--primary:var(--color-blue-600);--primary-foreground:var(--color-blue-50)}:is(.theme-blue,.theme-blue-scaled):is(.dark *){--primary:var(--color-blue-500);--primary-foreground:var(--color-blue-50)}.theme-green,.theme-green-scaled,:is(.theme-green,.theme-green-scaled):is(.dark *){--primary:var(--color-lime-600);--primary-foreground:var(--color-lime-50)}.theme-amber,.theme-amber-scaled{--primary:var(--color-amber-600);--primary-foreground:var(--color-amber-50)}:is(.theme-amber,.theme-amber-scaled):is(.dark *){--primary:var(--color-amber-500);--primary-foreground:var(--color-amber-50)}.theme-mono,.theme-mono-scaled{--font-sans:var(--font-mono);--primary:var(--color-neutral-600);--primary-foreground:var(--color-neutral-50)}:is(.theme-mono,.theme-mono-scaled):is(.dark *){--primary:var(--color-neutral-500);--primary-foreground:var(--color-neutral-50)}:is(.theme-mono,.theme-mono-scaled) .rounded-xs,:is(.theme-mono,.theme-mono-scaled) .rounded-sm,:is(.theme-mono,.theme-mono-scaled) .rounded-md,:is(.theme-mono,.theme-mono-scaled) .rounded-lg,:is(.theme-mono,.theme-mono-scaled) .rounded-xl{border-radius:0;border-radius:0!important}:is(.theme-mono,.theme-mono-scaled) .shadow-xs,:is(.theme-mono,.theme-mono-scaled) .shadow-sm,:is(.theme-mono,.theme-mono-scaled) .shadow-md,:is(.theme-mono,.theme-mono-scaled) .shadow-lg,:is(.theme-mono,.theme-mono-scaled) .shadow-xl{--tw-shadow:0 0 #0000!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}:is(.theme-mono,.theme-mono-scaled) [data-slot=toggle-group],:is(.theme-mono,.theme-mono-scaled) [data-slot=toggle-group-item]{--tw-shadow:0 0 #0000!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important;border-radius:0!important}:root{--radius:.625rem;--background:oklch(100% 0 0);--foreground:oklch(14.5% 0 0);--card:oklch(100% 0 0);--card-foreground:oklch(14.5% 0 0);--popover:oklch(100% 0 0);--popover-foreground:oklch(14.5% 0 0);--primary:oklch(35% .15 350);--primary-foreground:oklch(98.5% 0 0);--secondary:oklch(97% 0 0);--secondary-foreground:oklch(20.5% 0 0);--muted:oklch(97% 0 0);--muted-foreground:oklch(55.6% 0 0);--accent:oklch(32% .15 350);--accent-foreground:oklch(98.5% 0 0);--destructive:oklch(57.7% .245 27.325);--border:oklch(92.2% 0 0);--input:oklch(92.2% 0 0);--ring:oklch(35% .15 350);--chart-1:oklch(64.6% .222 41.116);--chart-2:oklch(60% .118 184.704);--chart-3:oklch(39.8% .07 227.392);--chart-4:oklch(82.8% .189 84.429);--chart-5:oklch(76.9% .188 70.08);--sidebar:oklch(98.5% 0 0);--sidebar-foreground:oklch(14.5% 0 0);--sidebar-primary:oklch(35% .15 350);--sidebar-primary-foreground:oklch(98.5% 0 0);--sidebar-accent:oklch(97% 0 0);--sidebar-accent-foreground:oklch(20.5% 0 0);--sidebar-border:oklch(92.2% 0 0);--sidebar-ring:oklch(35% .15 350);--iai-primary:#8b1538;--iai-secondary:#a91b3c;--iai-accent:#6b1227}.dark{--background:oklch(14.5% 0 0);--foreground:oklch(98.5% 0 0);--card:oklch(20.5% 0 0);--card-foreground:oklch(98.5% 0 0);--popover:oklch(26.9% 0 0);--popover-foreground:oklch(98.5% 0 0);--primary:oklch(45% .18 350);--primary-foreground:oklch(98.5% 0 0);--secondary:oklch(26.9% 0 0);--secondary-foreground:oklch(98.5% 0 0);--muted:oklch(26.9% 0 0);--muted-foreground:oklch(70.8% 0 0);--accent:oklch(42% .18 350);--accent-foreground:oklch(98.5% 0 0);--destructive:oklch(70.4% .191 22.216);--border:oklch(100% 0 0/.1);--input:oklch(100% 0 0/.15);--ring:oklch(45% .18 350);--chart-1:oklch(48.8% .243 264.376);--chart-2:oklch(69.6% .17 162.48);--chart-3:oklch(76.9% .188 70.08);--chart-4:oklch(62.7% .265 303.9);--chart-5:oklch(64.5% .246 16.439);--sidebar:oklch(20.5% 0 0);--sidebar-foreground:oklch(98.5% 0 0);--sidebar-primary:oklch(45% .18 350);--sidebar-primary-foreground:oklch(98.5% 0 0);--sidebar-accent:oklch(26.9% 0 0);--sidebar-accent-foreground:oklch(98.5% 0 0);--sidebar-border:oklch(100% 0 0/.1);--sidebar-ring:oklch(45% .18 350)}::view-transition-old(root){mix-blend-mode:normal;animation:none}::view-transition-new(root){mix-blend-mode:normal;animation:none}::view-transition-old(root){z-index:0}::view-transition-new(root){z-index:1}@keyframes reveal{0%{clip-path:circle(0% at var(--x,50%)var(--y,50%));opacity:.7}to{clip-path:circle(150% at var(--x,50%)var(--y,50%));opacity:1}}::view-transition-new(root){animation:.4s ease-in-out forwards reveal}@property --tw-translate-x{syntax:\"*\";inherits:false;initial-value:0}@property --tw-translate-y{syntax:\"*\";inherits:false;initial-value:0}@property --tw-translate-z{syntax:\"*\";inherits:false;initial-value:0}@property --tw-scale-x{syntax:\"*\";inherits:false;initial-value:1}@property --tw-scale-y{syntax:\"*\";inherits:false;initial-value:1}@property --tw-scale-z{syntax:\"*\";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:\"*\";inherits:false}@property --tw-rotate-y{syntax:\"*\";inherits:false}@property --tw-rotate-z{syntax:\"*\";inherits:false}@property --tw-skew-x{syntax:\"*\";inherits:false}@property --tw-skew-y{syntax:\"*\";inherits:false}@property --tw-space-y-reverse{syntax:\"*\";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:\"*\";inherits:false;initial-value:0}@property --tw-border-style{syntax:\"*\";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:\"*\";inherits:false}@property --tw-gradient-from{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:\"*\";inherits:false}@property --tw-gradient-via-stops{syntax:\"*\";inherits:false}@property --tw-gradient-from-position{syntax:\"<length-percentage>\";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:\"<length-percentage>\";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:\"<length-percentage>\";inherits:false;initial-value:100%}@property --tw-leading{syntax:\"*\";inherits:false}@property --tw-font-weight{syntax:\"*\";inherits:false}@property --tw-tracking{syntax:\"*\";inherits:false}@property --tw-ordinal{syntax:\"*\";inherits:false}@property --tw-slashed-zero{syntax:\"*\";inherits:false}@property --tw-numeric-figure{syntax:\"*\";inherits:false}@property --tw-numeric-spacing{syntax:\"*\";inherits:false}@property --tw-numeric-fraction{syntax:\"*\";inherits:false}@property --tw-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:\"*\";inherits:false}@property --tw-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:\"*\";inherits:false}@property --tw-inset-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:\"*\";inherits:false}@property --tw-ring-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:\"*\";inherits:false}@property --tw-inset-ring-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:\"*\";inherits:false}@property --tw-ring-offset-width{syntax:\"<length>\";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:\"*\";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:\"*\";inherits:false;initial-value:solid}@property --tw-blur{syntax:\"*\";inherits:false}@property --tw-brightness{syntax:\"*\";inherits:false}@property --tw-contrast{syntax:\"*\";inherits:false}@property --tw-grayscale{syntax:\"*\";inherits:false}@property --tw-hue-rotate{syntax:\"*\";inherits:false}@property --tw-invert{syntax:\"*\";inherits:false}@property --tw-opacity{syntax:\"*\";inherits:false}@property --tw-saturate{syntax:\"*\";inherits:false}@property --tw-sepia{syntax:\"*\";inherits:false}@property --tw-drop-shadow{syntax:\"*\";inherits:false}@property --tw-drop-shadow-color{syntax:\"*\";inherits:false}@property --tw-drop-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:\"*\";inherits:false}@property --tw-backdrop-blur{syntax:\"*\";inherits:false}@property --tw-backdrop-brightness{syntax:\"*\";inherits:false}@property --tw-backdrop-contrast{syntax:\"*\";inherits:false}@property --tw-backdrop-grayscale{syntax:\"*\";inherits:false}@property --tw-backdrop-hue-rotate{syntax:\"*\";inherits:false}@property --tw-backdrop-invert{syntax:\"*\";inherits:false}@property --tw-backdrop-opacity{syntax:\"*\";inherits:false}@property --tw-backdrop-saturate{syntax:\"*\";inherits:false}@property --tw-backdrop-sepia{syntax:\"*\";inherits:false}@property --tw-duration{syntax:\"*\";inherits:false}@property --tw-ease{syntax:\"*\";inherits:false}@property --tw-content{syntax:\"*\";inherits:false;initial-value:\"\"}@keyframes spin{to{transform:rotate(360deg)}}@keyframes ping{75%,to{opacity:0;transform:scale(2)}}@keyframes pulse{50%{opacity:.5}}@keyframes bounce{0%,to{animation-timing-function:cubic-bezier(.8,0,1,1);transform:translateY(-25%)}50%{animation-timing-function:cubic-bezier(0,0,.2,1);transform:none}}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@keyframes accordion-down{0%{height:0}to{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))))}}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))))}to{height:0}}@keyframes caret-blink{0%,70%,to{opacity:1}20%,50%{opacity:0}}"], "sourceRoot": ""}