{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|static|.*\\..*|_static|_vercel).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "beJQ8Mqjxg_irlwl19xOI", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "a69bb8c9b4b1ef15bf420be45c59c800", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4b0355ff033c5d98ad8042fa5d2d82bafdfec39ee2e9b703b9ccd17bf8e0bfd2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7eccba5931ff63a591b1c732fe863448799d3f8449ad86347e42f2d41605cbae"}}}, "functions": {}, "sortedMiddleware": ["/"]}