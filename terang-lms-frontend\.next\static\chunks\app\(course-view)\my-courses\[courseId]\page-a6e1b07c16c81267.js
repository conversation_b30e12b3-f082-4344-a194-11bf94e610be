try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="edfc1240-fe3a-4271-b7cf-6e22f6aaaeea",e._sentryDebugIdIdentifier="sentry-dbid-edfc1240-fe3a-4271-b7cf-6e22f6aaaeea")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9188],{12108:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},12800:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>i});var a=s(95155),n=s(12115),l=s(25667),r=s(64269);let i=l.bL,o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(l.B8,{ref:t,className:(0,r.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...n})});o.displayName=l.B8.displayName;let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(l.l9,{ref:t,className:(0,r.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer",s),...n})});c.displayName=l.l9.displayName;let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,a.jsx)(l.UC,{ref:t,className:(0,r.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n})});d.displayName=l.UC.displayName},15215:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(96063).A)("Award01Icon",[["path",{d:"M12 12V18",stroke:"currentColor",key:"k0"}],["path",{d:"M12 18C10.3264 18 8.86971 19.012 8.11766 20.505C7.75846 21.218 8.27389 22 8.95877 22H15.0412C15.7261 22 16.2415 21.218 15.8823 20.505C15.1303 19.012 13.6736 18 12 18Z",stroke:"currentColor",key:"k1"}],["path",{d:"M5 5H3.98471C2.99819 5 2.50493 5 2.20017 5.37053C1.89541 5.74106 1.98478 6.15597 2.16352 6.9858C2.50494 8.57086 3.24548 9.9634 4.2489 11",stroke:"currentColor",key:"k2"}],["path",{d:"M19 5H20.0153C21.0018 5 21.4951 5 21.7998 5.37053C22.1046 5.74106 22.0152 6.15597 21.8365 6.9858C21.4951 8.57086 20.7545 9.9634 19.7511 11",stroke:"currentColor",key:"k3"}],["path",{d:"M12 12C15.866 12 19 8.8831 19 5.03821C19 4.93739 18.9978 4.83707 18.9936 4.73729C18.9509 3.73806 18.9295 3.23845 18.2523 2.61922C17.5751 2 16.8247 2 15.324 2H8.67596C7.17526 2 6.42492 2 5.74772 2.61922C5.07051 3.23844 5.04915 3.73806 5.00642 4.73729C5.00215 4.83707 5 4.93739 5 5.03821C5 8.8831 8.13401 12 12 12Z",stroke:"currentColor",key:"k4"}]])},16325:(e,t,s)=>{"use strict";s.d(t,{Bc:()=>r,ZI:()=>c,k$:()=>o,m_:()=>i});var a=s(95155);s(12115);var n=s(47520),l=s(64269);function r(e){let{delayDuration:t=0,...s}=e;return(0,a.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...s,"data-sentry-element":"TooltipPrimitive.Provider","data-sentry-component":"TooltipProvider","data-sentry-source-file":"tooltip.tsx"})}function i(e){let{...t}=e;return(0,a.jsx)(r,{"data-sentry-element":"TooltipProvider","data-sentry-component":"Tooltip","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsx)(n.bL,{"data-slot":"tooltip",...t,"data-sentry-element":"TooltipPrimitive.Root","data-sentry-source-file":"tooltip.tsx"})})}function o(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"tooltip-trigger",...t,"data-sentry-element":"TooltipPrimitive.Trigger","data-sentry-component":"TooltipTrigger","data-sentry-source-file":"tooltip.tsx"})}function c(e){let{className:t,sideOffset:s=0,children:r,...i}=e;return(0,a.jsx)(n.ZL,{"data-sentry-element":"TooltipPrimitive.Portal","data-sentry-component":"TooltipContent","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:s,className:(0,l.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...i,"data-sentry-element":"TooltipPrimitive.Content","data-sentry-source-file":"tooltip.tsx",children:[r,(0,a.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]","data-sentry-element":"TooltipPrimitive.Arrow","data-sentry-source-file":"tooltip.tsx"})]})})}},24033:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},33513:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(96063).A)("ArrowLeft01Icon",[["path",{d:"M15 6C15 6 9.00001 10.4189 9 12C8.99999 13.5812 15 18 15 18",stroke:"currentColor",key:"k0"}]])},38004:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>i,rr:()=>h,zM:()=>o});var a=s(95155);s(12115);var n=s(89511),l=s(65229),r=s(64269);function i(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"dialog",...t,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function o(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"dialog-trigger",...t,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function c(e){let{...t}=e;return(0,a.jsx)(n.ZL,{"data-slot":"dialog-portal",...t,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m(e){let{className:t,children:s,...i}=e;return(0,a.jsxs)(c,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(d,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[s,(0,a.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(l.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",t),...s,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},55170:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>em});var a=s(95155),n=s(12115),l=s(20764),r=s(26737),i=s(38004),o=s(12800),c=s(88021),d=s(33513),m=s(96063);let u=(0,m.A)("Building02Icon",[["path",{d:"M15 2H9C5.69067 2 5 2.69067 5 6V22H19V6C19 2.69067 18.3093 2 15 2Z",stroke:"currentColor",key:"k0"}],["path",{d:"M3 22H21",stroke:"currentColor",key:"k1"}],["path",{d:"M15 22V19C15 17.3453 14.6547 17 13 17H11C9.34533 17 9 17.3453 9 19V22",stroke:"currentColor",key:"k2"}],["path",{d:"M13.5 6H10.5M13.5 9.5H10.5M13.5 13H10.5",stroke:"currentColor",key:"k3"}]]),x=(0,m.A)("Calendar01Icon",[["path",{d:"M18 2V4M6 2V4",stroke:"currentColor",key:"k0"}],["path",{d:"M10 17L9.99999 13.3472C9.99999 13.1555 9.86325 13 9.69458 13H9M13.6297 17L14.9842 13.3492C15.0475 13.1785 14.9128 13 14.7207 13H13",stroke:"currentColor",key:"k1"}],["path",{d:"M2.5 12.2432C2.5 7.88594 2.5 5.70728 3.75212 4.35364C5.00424 3 7.01949 3 11.05 3H12.95C16.9805 3 18.9958 3 20.2479 4.35364C21.5 5.70728 21.5 7.88594 21.5 12.2432V12.7568C21.5 17.1141 21.5 19.2927 20.2479 20.6464C18.9958 22 16.9805 22 12.95 22H11.05C7.01949 22 5.00424 22 3.75212 20.6464C2.5 19.2927 2.5 17.1141 2.5 12.7568V12.2432Z",stroke:"currentColor",key:"k2"}],["path",{d:"M6 8H18",stroke:"currentColor",key:"k3"}]]);var p=s(69264);let h=(0,m.A)("ChartIcon",[["path",{d:"M2 21.5L22 21.5",stroke:"currentColor",key:"k0"}],["path",{d:"M18 15.5H18.009M18 18.5H18.009",stroke:"currentColor",key:"k1"}],["path",{d:"M6 18.5H6.00898M6 15.5H6.00898M6 12.5H6.00898M6 9.5H6.00898",stroke:"currentColor",key:"k2"}],["path",{d:"M7.79063 5.39186L16.2183 9.5904M8 4.5C8 5.60457 7.10457 6.5 6 6.5C4.89543 6.5 4 5.60457 4 4.5C4 3.39543 4.89543 2.5 6 2.5C7.10457 2.5 8 3.39543 8 4.5ZM20 10.5C20 11.6046 19.1046 12.5 18 12.5C16.8954 12.5 16 11.6046 16 10.5C16 9.39543 16.8954 8.5 18 8.5C19.1046 8.5 20 9.39543 20 10.5Z",stroke:"currentColor",key:"k3"}]]);var y=s(15215);let f=(0,m.A)("Download01Icon",[["path",{d:"M3.09502 10C3.03241 10.457 3 10.9245 3 11.4C3 16.7019 7.02944 21 12 21C16.9706 21 21 16.7019 21 11.4C21 10.9245 20.9676 10.457 20.905 10",stroke:"currentColor",key:"k0"}],["path",{d:"M12 13L12 3M12 13C11.2998 13 9.99153 11.0057 9.5 10.5M12 13C12.7002 13 14.0085 11.0057 14.5 10.5",stroke:"currentColor",key:"k1"}]]);var g=s(52619),j=s.n(g),b=s(20063),N=s(9005),v=s(99223),C=s(47886),k=s(24033),w=s(27937),A=s(99708),z=s(61362),T=s(16325);let P=e=>{let{title:t,icon:s,isUnlocked:n,isCompleted:l=!1,children:r,level:i,isExpanded:o=!1,onToggle:c,onClick:d,hasChildren:m=!1,isActive:u=!1}=e,x=t.length>20;return(0,a.jsxs)("div",{className:"select-none","data-sentry-component":"TreeNode","data-sentry-source-file":"tree-node.tsx",children:[(0,a.jsx)(T.Bc,{"data-sentry-element":"TooltipProvider","data-sentry-source-file":"tree-node.tsx",children:(0,a.jsxs)(T.m_,{delayDuration:500,"data-sentry-element":"Tooltip","data-sentry-source-file":"tree-node.tsx",children:[(0,a.jsx)(T.k$,{asChild:!0,"data-sentry-element":"TooltipTrigger","data-sentry-source-file":"tree-node.tsx",children:(0,a.jsxs)("button",{onClick:()=>{m&&c?c():d&&d()},className:"flex w-full items-center space-x-3 rounded-lg px-4 py-3 text-left text-base transition-colors ".concat(n?u?"bg-blue-100 text-blue-800 border-2 border-blue-300 hover:bg-blue-150":"text-blue-700 hover:bg-blue-50":"cursor-not-allowed text-gray-400"),style:{paddingLeft:"".concat(20*i+16,"px")},disabled:!n,children:[m&&(0,a.jsx)("span",{className:"flex h-5 w-5 items-center justify-center",children:o?(0,a.jsx)(k.A,{className:"h-4 w-4"}):(0,a.jsx)(w.A,{className:"h-4 w-4"})}),(0,a.jsx)("span",{className:"flex h-5 w-5 items-center justify-center",children:s}),(0,a.jsx)("span",{className:"flex-1 font-medium ".concat(t.length>35?"text-sm leading-tight":"truncate"),children:t.length>35?(0,a.jsx)("span",{className:"block",children:t}):t}),!n&&(0,a.jsx)(A.A,{className:"h-4 w-4"}),l&&(0,a.jsx)(z.A,{className:"h-4 w-4 text-green-500"})]})}),x&&(0,a.jsx)(T.ZI,{side:"right",className:"max-w-xs",children:(0,a.jsx)("p",{children:t})})]})}),o&&r&&(0,a.jsx)("div",{children:r})]})};var S=s(66094),M=s(80003),q=s(47937),D=s(28446),E=s(85921),I=s(89715),U=s(71847);let B=(0,U.A)("BookMarked",[["path",{d:"M10 2v8l3-3 3 3V2",key:"sqw3rj"}],["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);var H=s(30814);let Q=(0,U.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]),L=e=>{let{course:t,onNavigate:s,expandedModules:n,expandedChapters:l,onToggleModule:r,onToggleChapter:i,currentModuleIndex:o}=e;return(0,a.jsxs)(S.Zp,{className:"sticky top-4 h-fit w-full max-w-full","data-sentry-element":"Card","data-sentry-component":"TableOfContents","data-sentry-source-file":"table-of-contents.tsx",children:[(0,a.jsx)(S.aR,{className:"pb-4","data-sentry-element":"CardHeader","data-sentry-source-file":"table-of-contents.tsx",children:(0,a.jsxs)(S.ZB,{className:"flex items-center text-xl","data-sentry-element":"CardTitle","data-sentry-source-file":"table-of-contents.tsx",children:[(0,a.jsx)(M.A,{className:"mr-3 h-6 w-6","data-sentry-element":"List","data-sentry-source-file":"table-of-contents.tsx"}),"Table of Contents"]})}),(0,a.jsxs)(S.Wu,{className:"max-h-[calc(100vh-12rem)] space-y-1 overflow-y-auto overflow-x-hidden","data-sentry-element":"CardContent","data-sentry-source-file":"table-of-contents.tsx",children:[t.modules.map((e,t)=>{let c=e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed;return(0,a.jsxs)(P,{id:e.id,title:e.title,icon:(0,a.jsx)(q.A,{className:"h-5 w-5"}),isUnlocked:e.isUnlocked,isCompleted:c,level:0,hasChildren:!0,isExpanded:n[e.id],onToggle:()=>r(e.id),isActive:o===t,children:[e.chapters.map(t=>{let n=t.contents.every(e=>e.isCompleted)&&t.quiz.isPassed;return(0,a.jsxs)(P,{id:t.id,title:t.title,icon:(0,a.jsx)(D.A,{className:"h-4 w-4"}),isUnlocked:t.isUnlocked,isCompleted:n,level:1,hasChildren:!0,isExpanded:l[t.id],onToggle:()=>i(t.id),children:[t.contents.map(n=>(0,a.jsx)(P,{id:n.id,title:n.title,icon:"video"===n.type?(0,a.jsx)(E.A,{className:"h-4 w-4 text-red-500"}):"pdf"===n.type?(0,a.jsx)(I.A,{className:"h-4 w-4 text-red-600"}):"zoom-recording"===n.type?(0,a.jsx)(E.A,{className:"h-4 w-4 text-blue-500"}):(0,a.jsx)(B,{className:"h-4 w-4 text-blue-500"}),isUnlocked:t.isUnlocked,isCompleted:n.isCompleted,level:2,onClick:()=>s(e.id,t.id,n.id)},n.id)),(0,a.jsx)(P,{id:"".concat(t.id,"-quiz"),title:"Chapter Quiz",icon:(0,a.jsx)(H.A,{className:"h-4 w-4"}),isUnlocked:t.contents.every(e=>e.isCompleted),isCompleted:t.quiz.isPassed,level:2,onClick:()=>s(e.id,t.id)},"".concat(t.id,"-quiz"))]},t.id)}),(0,a.jsx)(P,{id:"".concat(e.id,"-quiz"),title:"Module Quiz",icon:(0,a.jsx)(B,{className:"h-4 w-4"}),isUnlocked:e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts)),isCompleted:e.moduleQuiz.isPassed,level:1,onClick:()=>s(e.id)},"".concat(e.id,"-quiz"))]},e.id)}),(0,a.jsx)(P,{id:"final-exam",title:"Final Exam",icon:(0,a.jsx)(Q,{className:"h-5 w-5"}),isUnlocked:t.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts))&&(e.moduleQuiz.isPassed||e.moduleQuiz.attempts>=e.moduleQuiz.maxAttempts)),isCompleted:t.finalExam.isPassed,level:0,"data-sentry-element":"TreeNode","data-sentry-source-file":"table-of-contents.tsx"},"final-exam")]})]})};var $=s(6504),Z=s(22133),O=s(42196);let _=(0,U.A)("Timer",[["line",{x1:"10",x2:"14",y1:"2",y2:"2",key:"14vaq8"}],["line",{x1:"12",x2:"15",y1:"14",y2:"11",key:"17fdiu"}],["circle",{cx:"12",cy:"14",r:"8",key:"1e1u0o"}]]);var K=s(12108),R=s(39867),V=s(57828);let F=(0,U.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),J=e=>{let{content:t,onToggleComplete:s,isExpanded:n,onToggleExpand:r}=e;console.log("ContentItem received content:",t);let i=t.title||("object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?(e=>{for(let t of e.split("\n")){let e=t.match(/^#{1,4}\s+(.*)$/);if(e&&e[1])return e[1].trim()}return null})(t.content.value):null)||"No Title";return(0,a.jsx)(S.Zp,{id:"content-".concat(t.id),className:"my-2 ml-6 border-l-4 border-l-blue-200 scroll-mt-20","data-sentry-element":"Card","data-sentry-component":"ContentItem","data-sentry-source-file":"content-item.tsx",children:(0,a.jsx)(S.Wu,{className:"py-3","data-sentry-element":"CardContent","data-sentry-source-file":"content-item.tsx",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex cursor-pointer items-center justify-between",onClick:r,children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-3",children:[(()=>{switch(t.type){case"video":return(0,a.jsx)(E.A,{className:"h-4 w-4 text-red-500"});case"pdf":return(0,a.jsx)(I.A,{className:"h-4 w-4 text-red-600"});case"zoom-recording":return(0,a.jsx)(E.A,{className:"h-4 w-4 text-blue-500"});case"image":return(0,a.jsx)(O.A,{className:"h-4 w-4 text-green-500"});default:return(0,a.jsx)(B,{className:"h-4 w-4 text-blue-500"})}})(),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:i}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-2",children:[(0,a.jsx)(c.E,{variant:"outline",className:"text-xs","data-sentry-element":"Badge","data-sentry-source-file":"content-item.tsx",children:(()=>{switch(t.type){case"video":return"Video";case"pdf":return"PDF Document";case"zoom-recording":return"Zoom Recording";case"image":return"Image";default:return"Reading Material"}})()}),t.duration&&(0,a.jsxs)(c.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(_,{className:"mr-1 h-3 w-3"}),t.duration," min"]})]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:n?(0,a.jsx)(K.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(k.A,{className:"h-4 w-4 text-gray-400"})})]}),n&&(0,a.jsxs)("div",{className:"mt-4 border-t pt-4 pl-7",children:["text"===t.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"prose prose-sm max-w-none text-gray-700",children:(0,a.jsx)($.oz,{remarkPlugins:[Z.A],components:{h1:e=>{let{node:t,...s}=e;return(0,a.jsx)("h1",{className:"mb-4 text-2xl font-bold text-gray-900",...s})},h2:e=>{let{node:t,...s}=e;return(0,a.jsx)("h2",{className:"mb-3 text-xl font-semibold text-gray-800",...s})},h3:e=>{let{node:t,...s}=e;return(0,a.jsx)("h3",{className:"mb-2 text-lg font-semibold text-gray-800",...s})},h4:e=>{let{node:t,...s}=e;return(0,a.jsx)("h4",{className:"mb-2 text-base font-semibold text-gray-700",...s})},p:e=>{let{node:t,...s}=e;return(0,a.jsx)("p",{className:"mb-3 leading-relaxed",...s})},ul:e=>{let{node:t,...s}=e;return(0,a.jsx)("ul",{className:"mb-3 ml-4 list-disc",...s})},ol:e=>{let{node:t,...s}=e;return(0,a.jsx)("ol",{className:"mb-3 ml-4 list-decimal",...s})},li:e=>{let{node:t,...s}=e;return(0,a.jsx)("li",{className:"mb-1",...s})},blockquote:e=>{let{node:t,...s}=e;return(0,a.jsx)("blockquote",{className:"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic",...s})},code:e=>{let{node:t,className:s,children:n,...l}=e;return/language-(\w+)/.exec(s||"")?(0,a.jsx)("code",{className:"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100",...l,children:n}):(0,a.jsx)("code",{className:"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm",...l,children:n})},pre:e=>{let{node:t,...s}=e;return(0,a.jsx)("pre",{className:"mb-4",...s})},table:e=>{let{node:t,...s}=e;return(0,a.jsx)("div",{className:"mb-4 overflow-x-auto",children:(0,a.jsx)("table",{className:"min-w-full rounded border border-gray-200",...s})})},thead:e=>{let{node:t,...s}=e;return(0,a.jsx)("thead",{className:"bg-gray-50",...s})},th:e=>{let{node:t,...s}=e;return(0,a.jsx)("th",{className:"border border-gray-200 px-3 py-2 text-left font-semibold",...s})},td:e=>{let{node:t,...s}=e;return(0,a.jsx)("td",{className:"border border-gray-200 px-3 py-2",...s})},hr:e=>{let{node:t,...s}=e;return(0,a.jsx)("hr",{className:"my-6 border-gray-300",...s})},strong:e=>{let{node:t,...s}=e;return(0,a.jsx)("strong",{className:"font-semibold text-gray-900",...s})},em:e=>{let{node:t,...s}=e;return(0,a.jsx)("em",{className:"italic",...s})}},children:"string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:Array.isArray(t.content)?t.content.map(e=>"text"===e.type?e.value:"").join(""):""})}),(0,a.jsxs)(l.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let e=window.open("","_blank");if(!e)return;let s="\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>".concat(t.title,"</title>\n          <style>\n            @media print {\n              @page {\n                size: A4;\n                margin: 2cm;\n              }\n              body {\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n                line-height: 1.6;\n                color: #333;\n              }\n            }\n            body {\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n              line-height: 1.6;\n              color: #333;\n              max-width: 800px;\n              margin: 0 auto;\n              padding: 20px;\n            }\n            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }\n            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }\n            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }\n            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }\n            p { margin-bottom: 1em; }\n            ul, ol { margin-bottom: 1em; padding-left: 2em; }\n            li { margin-bottom: 0.25em; }\n            blockquote {\n              border-left: 4px solid #3182ce;\n              background: #ebf8ff;\n              padding: 1em;\n              margin: 1em 0;\n              font-style: italic;\n            }\n            code {\n              background: #f7fafc;\n              padding: 0.2em 0.4em;\n              border-radius: 3px;\n              font-family: 'Courier New', monospace;\n              font-size: 0.9em;\n            }\n            pre {\n              background: #2d3748;\n              color: #f7fafc;\n              padding: 1em;\n              border-radius: 5px;\n              overflow-x: auto;\n              margin: 1em 0;\n            }\n            pre code {\n              background: none;\n              padding: 0;\n              color: inherit;\n            }\n            table {\n              border-collapse: collapse;\n              width: 100%;\n              margin: 1em 0;\n            }\n            th, td {\n              border: 1px solid #e2e8f0;\n              padding: 0.5em;\n              text-align: left;\n            }\n            th {\n              background: #f7fafc;\n              font-weight: 600;\n            }\n            hr {\n              border: none;\n              height: 1px;\n              background: #e2e8f0;\n              margin: 2em 0;\n            }\n            strong { font-weight: 600; }\n            em { font-style: italic; }\n          </style>\n        </head>\n        <body>\n          <h1>").concat(t.title,'</h1>\n          <div id="markdown-content"></div>\n        </body>\n      </html>\n    ');e.document.write(s),e.document.close();let a=e.document.getElementById("markdown-content");if(a){let e="";"string"==typeof t.content?e=t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?e=t.content.value:Array.isArray(t.content)&&(e=t.content.map(e=>"text"===e.type?e.value:"").join("")),a.innerHTML=e=(e="<p>"+(e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/^### (.*$)/gim,"<h3>$1</h3>")).replace(/^## (.*$)/gim,"<h2>$1</h2>")).replace(/^# (.*$)/gim,"<h1>$1</h1>")).replace(/\*\*(.*)\*\*/gim,"<strong>$1</strong>")).replace(/\*(.*)\*/gim,"<em>$1</em>")).replace(/^\* (.*$)/gim,"<li>$1</li>")).replace(/(<li>.*<\/li>)/gim,"<ul>$1</ul>")).replace(/^\d+\. (.*$)/gim,"<li>$1</li>")).replace(/\n\n/g,"</p><p>"))+"</p>").replace(/<p><\/p>/g,"")}setTimeout(()=>{e.focus(),e.print(),e.close()},250)},children:[(0,a.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Download as PDF"]})]}):"pdf"===t.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("iframe",{src:"".concat("string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"","#toolbar=0&navpanes=0"),className:"w-full h-96 rounded border",title:t.title}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(l.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let e="string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"";e&&window.open(e,"_blank")},children:[(0,a.jsx)(V.A,{className:"mr-2 h-4 w-4"}),"Open in New Tab"]}),(0,a.jsxs)(l.$,{size:"sm",variant:"outline",className:"text-gray-600 hover:bg-gray-50",onClick:()=>{let e="string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"";if(e){let s=document.createElement("a");s.href=e,s.download=t.title||"document.pdf",document.body.appendChild(s),s.click(),document.body.removeChild(s)}},children:[(0,a.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Download"]})]})]}):"image"===t.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("img",{src:"string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"",alt:t.title||"Image",className:"max-w-full h-auto rounded border"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(l.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let e="string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"";e&&window.open(e,"_blank")},children:[(0,a.jsx)(F,{className:"mr-2 h-4 w-4"}),"Open in New Tab"]}),(0,a.jsxs)(l.$,{size:"sm",variant:"outline",className:"text-gray-600 hover:bg-gray-50",onClick:()=>{let e="string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"";if(e){let s=document.createElement("a");s.href=e,s.download=t.title||"image.jpg",document.body.appendChild(s),s.click(),document.body.removeChild(s)}},children:[(0,a.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Download"]})]})]}):"video"===t.type||"zoom-recording"===t.type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"aspect-video w-full overflow-hidden rounded-lg bg-gray-100",children:(()=>{let e="string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"";if(!e)return(0,a.jsx)("div",{className:"flex h-full w-full items-center justify-center text-center text-gray-500",children:"No video URL provided."});if(e.includes("youtube.com/watch?v=")||e.includes("youtu.be/")){var s;let n=(null==(s=e.split("v=")[1])?void 0:s.split("&")[0])||e.split("/").pop();return(0,a.jsx)("iframe",{className:"h-full w-full",src:"https://www.youtube.com/embed/".concat(n),frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,title:t.title})}if(!e.includes("vimeo.com/"))return(0,a.jsx)("video",{controls:!0,className:"h-full w-full",src:e,title:t.title,children:"Your browser does not support the video tag."});{let s=e.split("/").pop();return(0,a.jsx)("iframe",{className:"h-full w-full",src:"https://player.vimeo.com/video/".concat(s),frameBorder:"0",allow:"autoplay; fullscreen; picture-in-picture",allowFullScreen:!0,title:t.title})}})()}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsxs)(l.$,{size:"sm",variant:"outline",className:"border-blue-200 text-blue-600 hover:bg-blue-50",onClick:()=>{let e="string"==typeof t.content?t.content:"object"==typeof t.content&&null!==t.content&&"value"in t.content&&"string"==typeof t.content.value?t.content.value:"";e&&window.open(e,"_blank")},children:[(0,a.jsx)(F,{className:"mr-2 h-4 w-4"}),"Open in New Tab"]})})]}):(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("div",{className:"flex aspect-video items-center justify-center rounded-lg bg-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"mx-auto mb-2 h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Unsupported Media Type or Invalid Content."})]})})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)(l.$,{size:"sm",variant:t.isCompleted?"default":"outline",className:"min-w-[120px] ".concat(t.isCompleted?"bg-green-600 text-white hover:bg-green-700":"text-gray-600 hover:bg-gray-50"),onClick:e=>{e.stopPropagation(),s()},children:[(0,a.jsx)(z.A,{className:"mr-2 h-4 w-4"}),t.isCompleted?"Completed":"Mark Complete"]})})]})]})})})};var W=s(26983),G=s(42529);let X=e=>{let{quiz:t,isUnlocked:s,onStartQuiz:n}=e;return(0,a.jsx)(S.Zp,{className:"my-3 ml-6 border-2 ".concat(s?"":"opacity-50"),"data-sentry-element":"Card","data-sentry-component":"QuizCard","data-sentry-source-file":"quiz-card.tsx",children:(0,a.jsx)(S.Wu,{className:"p-4","data-sentry-element":"CardContent","data-sentry-source-file":"quiz-card.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"rounded-lg p-2 ".concat((()=>{switch(t.type){case"chapter":return"bg-blue-100 text-blue-700 border-blue-200";case"module":return"bg-purple-100 text-purple-700 border-purple-200";case"final":return"bg-red-100 text-red-700 border-red-200";default:return"bg-gray-100 text-gray-700 border-gray-200"}})()),children:(()=>{switch(t.type){case"chapter":default:return(0,a.jsx)(H.A,{className:"h-4 w-4"});case"module":return(0,a.jsx)(B,{className:"h-4 w-4"});case"final":return(0,a.jsx)(Q,{className:"h-4 w-4"})}})()}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:t.title}),(0,a.jsxs)("div",{className:"mt-1 flex items-center space-x-3",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Min. Score: ",t.minimumScore,"%"]}),t.timeLimit&&(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:[(0,a.jsx)(W.A,{className:"mr-1 inline h-3 w-3"}),t.timeLimit," min"]}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Attempts: ",t.attempts,"/",t.maxAttempts]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t.isPassed&&(0,a.jsxs)(c.E,{className:"bg-green-100 text-green-700",children:[(0,a.jsx)(G.A,{className:"mr-1 h-3 w-3"}),"Passed (",t.lastScore,"%)"]}),(0,a.jsx)(l.$,{size:"sm",disabled:!s||t.attempts>=t.maxAttempts,onClick:n,variant:t.isPassed?"outline":"default","data-sentry-element":"Button","data-sentry-source-file":"quiz-card.tsx",children:0===t.attempts?"Start Quiz":t.isPassed?"Retake":"Continue"})]})]})})})},Y=e=>{let{quiz:t,isOpen:s,onClose:o,onComplete:d}=e,[m,u]=(0,n.useState)(0),[x,p]=(0,n.useState)({}),[h,y]=(0,n.useState)(t.timeLimit?60*t.timeLimit:null),[f,g]=(0,n.useState)(!1);n.useEffect(()=>{if(s&&null!==h&&h>0){let e=setInterval(()=>{y(e=>null===e||e<=1?(b(),0):e-1)},1e3);return()=>clearInterval(e)}},[s,h]);let j=(e,t)=>{p(s=>({...s,[e]:t}))},b=()=>{if(f)return;g(!0);let e=0;t.questions.forEach(t=>{let s=x[t.id];if("multiple_choice"===t.type||"multiple-choice"===t.type)if(s===t.correctAnswer)e++;else{var a;let n=null==(a=t.options)?void 0:a[s];n&&"object"==typeof n&&n.isCorrect&&e++}else"true_false"===t.type||"true-false"===t.type?s===t.correctAnswer&&e++:"essay"===t.type&&s&&""!==s.trim()&&e++});let s=Math.round(e/t.questions.length*100);setTimeout(()=>{d(s),g(!1),p({}),u(0),t.timeLimit&&y(60*t.timeLimit)},1e3)};if(!s||0===t.questions.length)return null;let N=t.questions[m],v=m===t.questions.length-1,C=void 0!==x[N.id];return(0,a.jsx)(i.lG,{open:s,onOpenChange:o,"data-sentry-element":"Dialog","data-sentry-component":"QuizModal","data-sentry-source-file":"quiz-modal.tsx",children:(0,a.jsxs)(i.Cf,{className:"max-h-[90vh] max-w-4xl overflow-y-auto p-6","data-sentry-element":"DialogContent","data-sentry-source-file":"quiz-modal.tsx",children:[(0,a.jsx)(i.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"quiz-modal.tsx",children:(0,a.jsxs)(i.L3,{className:"flex items-center justify-between","data-sentry-element":"DialogTitle","data-sentry-source-file":"quiz-modal.tsx",children:[(0,a.jsx)("span",{children:t.title}),null!==h&&(0,a.jsxs)(c.E,{variant:"outline",className:"border-red-200 text-red-600",children:[(0,a.jsx)(W.A,{className:"mr-1 h-4 w-4"}),(e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(h)]})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:["Question ",m+1," of ",t.questions.length]}),(0,a.jsxs)("span",{children:[Math.round((m+1)/t.questions.length*100),"% Complete"]})]}),(0,a.jsx)(r.k,{value:(m+1)/t.questions.length*100,"data-sentry-element":"Progress","data-sentry-source-file":"quiz-modal.tsx"})]}),(0,a.jsx)(S.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"quiz-modal.tsx",children:(0,a.jsxs)(S.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"quiz-modal.tsx",children:[(0,a.jsx)("h3",{className:"mb-4 text-lg font-medium",children:"string"==typeof N.question?N.question:Array.isArray(N.question)?N.question.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:"Question image ".concat(t),className:"inline-block max-h-16 object-contain ml-2"})]},t)):(0,a.jsx)("span",{children:String(N.question)})}),("multiple_choice"===N.type||"multiple-choice"===N.type)&&N.options&&(0,a.jsx)("div",{className:"space-y-3",children:N.options.map((e,t)=>(0,a.jsxs)("label",{className:"flex cursor-pointer items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:N.id,value:t,checked:x[N.id]===t,onChange:()=>j(N.id,t),className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"string"==typeof e?e:Array.isArray(e.content)?e.content.map((e,t)=>(0,a.jsxs)(n.Fragment,{children:["text"===e.type&&(0,a.jsx)("span",{children:e.value}),"image"===e.type&&e.value&&(0,a.jsx)("img",{src:e.value,alt:"Option image ".concat(t),className:"inline-block max-h-8 object-contain ml-1"})]},t)):(0,a.jsx)("span",{children:String(e.content||e)})})]},t))}),("true_false"===N.type||"true-false"===N.type)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex cursor-pointer items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:N.id,value:"true",checked:"true"===x[N.id],onChange:()=>j(N.id,"true"),className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"True"})]}),(0,a.jsxs)("label",{className:"flex cursor-pointer items-center space-x-3",children:[(0,a.jsx)("input",{type:"radio",name:N.id,value:"false",checked:"false"===x[N.id],onChange:()=>j(N.id,"false"),className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"False"})]})]}),"essay"===N.type&&(0,a.jsx)("textarea",{className:"w-full resize-none rounded-lg border p-3 focus:border-transparent focus:ring-2 focus:ring-blue-500",rows:6,placeholder:"Type your answer here...",value:x[N.id]||"",onChange:e=>j(N.id,e.target.value)})]})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>u(e=>Math.max(0,e-1)),disabled:0===m||f,"data-sentry-element":"Button","data-sentry-source-file":"quiz-modal.tsx",children:"Previous"}),(0,a.jsx)("div",{className:"flex space-x-2",children:v?(0,a.jsx)(l.$,{onClick:b,disabled:!C||f,className:"bg-green-600 hover:bg-green-700",children:f?"Submitting...":"Submit Quiz"}):(0,a.jsx)(l.$,{onClick:()=>u(e=>e+1),disabled:!C||f,children:"Next"})})]})]})]})})},ee=e=>{let{chapter:t,expandedContents:s,onToggleContent:n,onToggleContentComplete:l,onStartQuiz:i,isExpanded:o,onToggleExpanded:c}=e,d=t.contents.filter(e=>e.isCompleted).length,m=t.contents.length;return(0,a.jsx)(S.Zp,{id:"chapter-".concat(t.id),className:"my-3 border-l-4 scroll-mt-20 ".concat(t.isUnlocked?"border-l-green-400":"border-l-gray-300"," ").concat(t.isUnlocked?"":"opacity-60"),"data-sentry-element":"Card","data-sentry-component":"ChapterSection","data-sentry-source-file":"chapter-section.tsx",children:(0,a.jsx)(S.Wu,{className:"p-4","data-sentry-element":"CardContent","data-sentry-source-file":"chapter-section.tsx",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex cursor-pointer items-center justify-between",onClick:()=>t.isUnlocked&&c(),children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-3",children:[(0,a.jsx)("div",{className:"rounded-lg p-2 ".concat(t.isUnlocked?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-500"),children:(0,a.jsx)(D.A,{className:"h-5 w-5","data-sentry-element":"Book","data-sentry-source-file":"chapter-section.tsx"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-medium",children:t.title}),!t.isUnlocked&&(0,a.jsx)(A.A,{className:"h-4 w-4 text-gray-400"})]}),(0,a.jsx)("div",{className:"mt-2 max-w-md",children:(0,a.jsx)(r.k,{value:m>0?d/m*100:0,className:"h-2","data-sentry-element":"Progress","data-sentry-source-file":"chapter-section.tsx"})}),(0,a.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:[d,"/",m," contents completed",t.quiz.isPassed&&" • Quiz passed"]})]})]}),t.isUnlocked&&(o?(0,a.jsx)(K.A,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(k.A,{className:"h-5 w-5 text-gray-400"}))]}),o&&t.isUnlocked&&(0,a.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:t.contents.map(e=>(0,a.jsx)(J,{content:e,onToggleComplete:()=>l(e.id),isExpanded:s[e.id]||!1,onToggleExpand:()=>n(e.id)},e.id))}),(0,a.jsx)(X,{quiz:t.quiz,isUnlocked:d===m,onStartQuiz:()=>i(t.quiz.id)})]})]})})})},et=e=>{let{module:t,expandedContents:s,expandedChapters:n,onToggleContent:l,onToggleContentComplete:i,onStartQuiz:o,isExpanded:c,onToggleExpanded:d,onToggleChapter:m,onExpandAllChapters:u,onCollapseAllChapters:x}=e,p=t.chapters.length,h=t.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts)).length,y=t.chapters.filter(e=>e.isUnlocked);return y.length>0&&y.every(e=>!0===n[e.id]),(0,a.jsxs)(S.Zp,{id:"module-".concat(t.id),className:"mb-6 shadow-md scroll-mt-20 ".concat(t.isUnlocked?"border-green-200":"border-gray-200"),"data-sentry-element":"Card","data-sentry-component":"ModuleSection","data-sentry-source-file":"module-section.tsx",children:[(0,a.jsx)(S.aR,{className:"border-b ".concat(t.isUnlocked?"":"opacity-60"),"data-sentry-element":"CardHeader","data-sentry-source-file":"module-section.tsx",children:(0,a.jsxs)("div",{className:"flex cursor-pointer items-center justify-between",onClick:()=>t.isUnlocked&&d(),children:[(0,a.jsxs)("div",{className:"flex flex-1 items-center space-x-4",children:[(0,a.jsx)("div",{className:"rounded-lg p-3 ".concat(t.isUnlocked?"bg-purple-100 text-purple-700":"bg-gray-100 text-gray-500"),children:(0,a.jsx)(q.A,{className:"h-6 w-6","data-sentry-element":"BookOpen","data-sentry-source-file":"module-section.tsx"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(S.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"module-section.tsx",children:t.title}),!t.isUnlocked&&(0,a.jsx)(A.A,{className:"h-4 w-4 text-gray-400"})]}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:t.description}),(0,a.jsx)("div",{className:"mt-3 max-w-md",children:(0,a.jsx)(r.k,{value:p>0?h/p*100:0,className:"h-3","data-sentry-element":"Progress","data-sentry-source-file":"module-section.tsx"})}),(0,a.jsxs)("p",{className:"mt-2 text-sm text-gray-500",children:[h,"/",p," chapters completed",t.moduleQuiz.isPassed&&" • Module quiz passed"]})]})]}),t.isUnlocked&&(c?(0,a.jsx)(K.A,{className:"h-6 w-6 text-gray-400"}):(0,a.jsx)(k.A,{className:"h-6 w-6 text-gray-400"}))]})}),c&&t.isUnlocked&&(0,a.jsxs)(S.Wu,{className:"pt-6",children:[(0,a.jsx)("div",{className:"space-y-2",children:t.chapters.map(e=>(0,a.jsx)(ee,{chapter:e,expandedContents:s,onToggleContent:l,onToggleContentComplete:i,onStartQuiz:o,isExpanded:n[e.id]||!1,onToggleExpanded:()=>m(e.id)},e.id))}),(0,a.jsxs)("div",{className:"mt-6 border-t pt-4",children:[(0,a.jsx)("h4",{className:"mb-3 font-medium text-purple-700",children:"Module Assessment"}),(0,a.jsx)(X,{quiz:t.moduleQuiz,isUnlocked:h===p,onStartQuiz:()=>o(t.moduleQuiz.id)})]})]})]})};s(15239);var es=s(65229),ea=s(52472),en=s(90368);let el=e=>{let{courseData:t,expandedModules:s,expandedChapters:r,expandedContents:i,onToggleModule:o,onToggleChapter:c,onToggleContent:d,onToggleContentComplete:m,onStartQuiz:u,onNavigateToSection:x,onExpandAllModules:p,onCollapseAllModules:h,onExpandAllChaptersInModule:y,onCollapseAllChaptersInModule:f}=e,[g,j]=(0,n.useState)(!0),[b,N]=(0,n.useState)(0);t.modules.filter(e=>e.isUnlocked).every(e=>s[e.id]);let v=t.modules[b],C=b>0,k=b<t.modules.length-1,A=e=>{let s=t.modules.findIndex(t=>t.id===e);-1!==s&&N(s)};return(0,a.jsx)("div",{className:"h-[calc(100vh-210px)] overflow-hidden","data-sentry-component":"CourseTab","data-sentry-source-file":"course-tab.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-4 min-w-0 h-full",children:[(0,a.jsx)("div",{className:"lg:col-span-1 min-w-0 overflow-y-auto",children:(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(L,{course:t,onNavigate:(e,t,s)=>{A(e),x(e,t,s)},expandedModules:s,expandedChapters:r,onToggleModule:o,onToggleChapter:c,currentModuleIndex:b,"data-sentry-element":"TableOfContents","data-sentry-source-file":"course-tab.tsx"})})}),(0,a.jsxs)("div",{className:"space-y-4 lg:col-span-3 min-w-0 overflow-y-auto",children:[g&&(0,a.jsxs)("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4 relative",children:[(0,a.jsx)("button",{onClick:()=>j(!1),className:"absolute top-3 right-3 p-1 rounded-full hover:bg-blue-100 transition-colors","aria-label":"Tutup struktur kursus",children:(0,a.jsx)(es.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsx)("h3",{className:"mb-2 font-semibold text-blue-900 pr-8",children:"Struktur Kursus"}),(0,a.jsx)("p",{className:"mb-3 text-sm text-blue-800",children:"Selesaikan semua modul secara berurutan. Setiap bab harus diselesaikan sebelum mengakses kuis. Kuis modul akan terbuka setelah menyelesaikan semua kuis bab."}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(B,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"Jalur Pembelajaran Berurutan"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(H.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"Kuis Interaktif"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ea.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{children:"Sertifikasi Profesional"})]})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:v&&(0,a.jsx)("div",{"data-module-id":v.id,children:(0,a.jsx)(et,{module:v,expandedContents:i,expandedChapters:r,onToggleContent:d,onToggleContentComplete:m,onStartQuiz:u,isExpanded:s[v.id]||!1,onToggleExpanded:()=>o(v.id),onToggleChapter:c,onExpandAllChapters:()=>y(v.id),onCollapseAllChapters:()=>f(v.id)})},v.id)}),(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between rounded-lg border bg-white p-4",children:[(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{C&&N(b-1)},disabled:!C,className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"course-tab.tsx",children:[(0,a.jsx)(en.A,{className:"h-4 w-4","data-sentry-element":"ChevronLeft","data-sentry-source-file":"course-tab.tsx"}),(0,a.jsx)("span",{children:"Modul Sebelumnya"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold",children:["Modul ",b+1," dari ",t.modules.length]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:null==v?void 0:v.title})]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>{k&&N(b+1)},disabled:!k,className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"course-tab.tsx",children:[(0,a.jsx)("span",{children:"Modul Selanjutnya"}),(0,a.jsx)(w.A,{className:"h-4 w-4","data-sentry-element":"ChevronRight","data-sentry-source-file":"course-tab.tsx"})]})]})]})]})})};var er=s(80534);let ei=e=>{let{courseData:t,overallProgress:s}=e;return(0,a.jsxs)("div",{className:"grid gap-6","data-sentry-component":"ProgressTab","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsxs)(S.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(S.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsxs)(S.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(er.A,{className:"h-5 w-5","data-sentry-element":"BarChart3","data-sentry-source-file":"progress-tab.tsx"}),(0,a.jsx)("span",{children:"Ringkasan Kemajuan Belajar"})]})}),(0,a.jsx)(S.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"mb-2 text-3xl font-bold text-blue-600",children:[Math.round(s),"%"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Kemajuan Keseluruhan"}),(0,a.jsx)(r.k,{value:s,className:"mt-2","data-sentry-element":"Progress","data-sentry-source-file":"progress-tab.tsx"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-2 text-3xl font-bold text-green-600",children:t.modules.filter(e=>e.moduleQuiz.isPassed).length}),(0,a.jsx)("p",{className:"text-gray-600",children:"Modul Selesai"}),(0,a.jsx)(r.k,{value:t.modules.filter(e=>e.moduleQuiz.isPassed).length/t.modules.length*100,className:"mt-2","data-sentry-element":"Progress","data-sentry-source-file":"progress-tab.tsx"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-2 text-3xl font-bold text-purple-600",children:"0"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Jam Belajar"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Pelacakan waktu segera tersedia"})]})]})})]}),(0,a.jsxs)(S.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(S.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsx)(S.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"progress-tab.tsx",children:"Detail Kemajuan Modul"})}),(0,a.jsx)(S.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsx)("div",{className:"space-y-4",children:t.modules.map(e=>{let t=e.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed).length,s=t/e.chapters.length*100;return(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-3 flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsx)(c.E,{variant:e.moduleQuiz.isPassed?"default":"outline",children:e.moduleQuiz.isPassed?"Selesai":"Sedang Belajar"})]}),(0,a.jsx)(r.k,{value:s,className:"mb-2"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,a.jsxs)("span",{children:[t,"/",e.chapters.length," bab selesai"]}),(0,a.jsxs)("span",{children:[Math.round(s),"%"]})]})]},e.id)})})})]}),(0,a.jsxs)(S.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"progress-tab.tsx",children:[(0,a.jsx)(S.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"progress-tab.tsx",children:(0,a.jsx)(S.ZB,{"data-sentry-element":"CardTitle","data-sentry-source-file":"progress-tab.tsx",children:"Performa Kuis"})}),(0,a.jsx)(S.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"progress-tab.tsx",children:(()=>{let e=[];return(t.modules.forEach(t=>{t.chapters.forEach(s=>{s.quiz.attempts>0&&e.push({...s.quiz,moduleName:t.title,chapterName:s.title,type:"chapter"})}),t.moduleQuiz.attempts>0&&e.push({...t.moduleQuiz,moduleName:t.title,chapterName:null,type:"module"})}),t.finalExam.attempts>0&&e.push({...t.finalExam,moduleName:"Final Assessment",chapterName:null,type:"final"}),0===e.length)?(0,a.jsxs)("div",{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(H.A,{className:"mx-auto mb-4 h-12 w-12 text-gray-400"}),(0,a.jsx)("p",{children:"Hasil kuis akan muncul di sini saat Anda menyelesaikan penilaian"})]}):(0,a.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.moduleName,e.chapterName?" • ".concat(e.chapterName):""]})]}),(0,a.jsx)(c.E,{variant:e.isPassed?"default":"destructive",className:e.isPassed?"bg-green-600 hover:bg-green-700 text-white":"",children:e.isPassed?"Lulus":"Tidak Lulus"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Skor:"}),(0,a.jsx)("div",{className:"font-medium",children:void 0!==e.lastScore?"".concat(e.lastScore,"%"):"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Diperlukan:"}),(0,a.jsxs)("div",{className:"font-medium",children:[e.minimumScore,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Percobaan:"}),(0,a.jsxs)("div",{className:"font-medium",children:[e.attempts,"/",e.maxAttempts]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Jenis:"}),(0,a.jsx)("div",{className:"font-medium capitalize",children:e.type})]})]}),void 0!==e.lastScore&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(r.k,{value:e.lastScore,className:"h-2 ".concat(e.isPassed?"text-green-600":"text-red-600")})})]},e.id))})})()})]})]})};var eo=s(78874);let ec=e=>{let{courseData:t,onStartQuiz:s}=e,n=t.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&e.moduleQuiz.isPassed);return(0,a.jsxs)(S.Zp,{className:"shadow-sm","data-sentry-element":"Card","data-sentry-component":"ExamTab","data-sentry-source-file":"exam-tab.tsx",children:[(0,a.jsx)(S.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"exam-tab.tsx",children:(0,a.jsxs)(S.ZB,{className:"flex items-center space-x-2","data-sentry-element":"CardTitle","data-sentry-source-file":"exam-tab.tsx",children:[(0,a.jsx)(Q,{className:"h-6 w-6","data-sentry-element":"Trophy","data-sentry-source-file":"exam-tab.tsx"}),(0,a.jsx)("span",{children:"Ujian Akhir Sertifikasi"})]})}),(0,a.jsx)(S.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"exam-tab.tsx",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-lg border border-amber-200 bg-amber-50 p-4",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold text-amber-800",children:"Persyaratan Ujian"}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm text-amber-700",children:[(0,a.jsx)("li",{children:"• Selesaikan semua modul dan lulus semua kuis modul"}),(0,a.jsxs)("li",{children:["• Nilai minimum lulus: ",t.finalExam.minimumScore,"%"]}),(0,a.jsxs)("li",{children:["• Batas waktu: ",t.finalExam.timeLimit," menit"]}),(0,a.jsxs)("li",{children:["• Maksimal percobaan: ",t.finalExam.maxAttempts]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Status Prasyarat"}),t.modules.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("span",{className:"text-sm",children:e.title}),e.moduleQuiz.isPassed?(0,a.jsx)(G.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(eo.A,{className:"h-5 w-5 text-red-500"})]},e.id))]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Informasi Ujian"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Percobaan Digunakan"}),(0,a.jsxs)("span",{className:"font-medium",children:[t.finalExam.attempts,"/",t.finalExam.maxAttempts]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Skor Terakhir"}),(0,a.jsx)("span",{className:"font-medium",children:t.finalExam.lastScore?"".concat(t.finalExam.lastScore,"%"):"N/A"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsx)(c.E,{variant:t.finalExam.isPassed?"default":"outline","data-sentry-element":"Badge","data-sentry-source-file":"exam-tab.tsx",children:t.finalExam.isPassed?"Lulus":"Belum Diambil"})]})]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4 text-center",children:[(0,a.jsxs)(l.$,{size:"lg",variant:"iai",disabled:t.finalExam.attempts>=t.finalExam.maxAttempts,onClick:()=>s(t.finalExam.id),"data-sentry-element":"Button","data-sentry-source-file":"exam-tab.tsx",children:[(0,a.jsx)(Q,{className:"mr-2 h-5 w-5","data-sentry-element":"Trophy","data-sentry-source-file":"exam-tab.tsx"}),0===t.finalExam.attempts?"Mulai Ujian Akhir":"Ulangi Ujian Akhir"]}),!n&&(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Selesaikan semua modul untuk membuka ujian akhir"})]})]})})]})},ed=e=>{var t;let{courseData:s,institution:n,overallProgress:r,onGenerateCertificate:i,onShowCertificate:o,onDownloadPDF:c}=e,d=async()=>{let e=C.qs.getUser(),t={studentName:(null==e?void 0:e.name)||"John Doe",courseName:s.name,courseCode:s.code,completionDate:s.certificate.completionDate||new Date().toISOString().split("T")[0],finalScore:s.finalExam.lastScore||0,instructorName:s.instructor,institutionName:n.name,certificateId:(0,v.K2)()};await (0,v.Ct)(t)};return(0,a.jsxs)(S.Zp,{"data-sentry-element":"Card","data-sentry-component":"CertificateTab","data-sentry-source-file":"certificate-tab.tsx",children:[(0,a.jsx)(S.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"certificate-tab.tsx",children:(0,a.jsxs)(S.ZB,{className:"flex items-center space-x-2",style:{color:null==(t=n.certificateTemplate)?void 0:t.primaryColor},"data-sentry-element":"CardTitle","data-sentry-source-file":"certificate-tab.tsx",children:[(0,a.jsx)(ea.A,{className:"h-6 w-6","data-sentry-element":"Award","data-sentry-source-file":"certificate-tab.tsx"}),(0,a.jsx)("span",{children:"Sertifikasi Profesional"})]})}),(0,a.jsx)(S.Wu,{className:"p-6","data-sentry-element":"CardContent","data-sentry-source-file":"certificate-tab.tsx",children:0===s.finalExam.attempts?(0,a.jsx)("div",{className:"space-y-6 text-center",children:(0,a.jsxs)("div",{className:"rounded-lg border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 p-8",children:[(0,a.jsx)(A.A,{className:"mx-auto mb-4 h-16 w-16 text-yellow-600"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-bold text-yellow-800",children:"Belum Mengikuti Final Exam"}),(0,a.jsx)("p",{className:"text-yellow-700",children:"Kamu belum mengikuti final exam. Selesaikan final exam terlebih dahulu untuk mendapatkan sertifikat."})]})}):s.certificate.isEligible&&s.certificate.isGenerated||s.certificate.isEligible?(0,a.jsxs)("div",{className:"space-y-6 text-center",children:[(0,a.jsxs)("div",{className:"rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8",children:[(0,a.jsx)(ea.A,{className:"mx-auto mb-4 h-16 w-16 text-green-600"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-bold text-green-800",children:"Selamat!"}),(0,a.jsxs)("p",{className:"text-green-700",children:["Anda telah berhasil menyelesaikan kursus ",s.name," dan memperoleh sertifikasi."]}),s.certificate.completionDate&&(0,a.jsxs)("p",{className:"mt-2 text-sm text-green-600",children:["Diselesaikan pada:"," ",new Date(s.certificate.completionDate).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,a.jsxs)(l.$,{onClick:o,className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(V.A,{className:"mr-2 h-4 w-4"}),"Lihat Sertifikat"]}),(0,a.jsxs)(l.$,{variant:"outline",className:"border-green-600 text-green-600 hover:bg-green-50",onClick:d,children:[(0,a.jsx)(R.A,{className:"mr-2 h-4 w-4"}),"Unduh PDF"]})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center",children:[(0,a.jsx)(A.A,{className:"mx-auto mb-4 h-16 w-16 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-2xl font-bold text-gray-700",children:"Persyaratan Sertifikat"}),(0,a.jsx)("p",{className:"mb-4 text-gray-600",children:"Selesaikan semua persyaratan kursus untuk memperoleh sertifikasi profesional Anda."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Penyelesaian Modul"}),s.modules.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("span",{className:"text-sm",children:e.title}),e.moduleQuiz.isPassed?(0,a.jsx)(G.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(eo.A,{className:"h-5 w-5 text-gray-400"})]},e.id))]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"Persyaratan Akhir"}),(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsxs)("span",{className:"text-sm",children:["Final Exam (Min. ",s.finalExam.minimumScore,"%)"]}),s.finalExam.isPassed?(0,a.jsx)(G.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(eo.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("span",{className:"text-sm",children:"Skor Keseluruhan (Min. 70%)"}),r>=70?(0,a.jsx)(G.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(eo.A,{className:"h-5 w-5 text-gray-400"})]})]})]})]})})]})};s(25647);let em=()=>{var e;let t=(0,b.useParams)(),s=(0,b.useRouter)(),m=(0,b.useSearchParams)(),g=t.courseId,{courseData:k,updateCourseProgress:w,getCourseById:A,isEnrolledInCourse:z}=(0,N.q)(),T=A(g)||k,[P,S]=(0,n.useState)({}),[M,q]=(0,n.useState)({}),[D,E]=(0,n.useState)({}),[I,U]=(0,n.useState)(null),[B,H]=(0,n.useState)(!1),[Q,L]=(0,n.useState)("course");(0,n.useEffect)(()=>{let e=m.get("tab");e&&L(e)},[m]);let $=(0,n.useCallback)(e=>{S(t=>({...t,[e]:!t[e]}))},[]),Z=(0,n.useCallback)(e=>{q(t=>({...t,[e]:!t[e]}))},[]),O=(0,n.useCallback)(e=>{E(t=>({...t,[e]:!t[e]}))},[]),_=(0,n.useCallback)(()=>{let e={};T.modules.forEach(t=>{t.isUnlocked&&(e[t.id]=!0)}),q(e)},[T.modules]),K=(0,n.useCallback)(()=>{q({})},[]),R=(0,n.useCallback)(e=>{let t=T.modules.find(t=>t.id===e);if(!t)return;let s={...D};t.chapters.forEach(e=>{e.isUnlocked&&(s[e.id]=!0)}),E(s)},[T.modules,D]),V=(0,n.useCallback)(e=>{let t=T.modules.find(t=>t.id===e);if(!t)return;q(t=>({...t,[e]:!1}));let s={...D};t.chapters.forEach(e=>{delete s[e.id]}),E(s)},[T.modules,D]),F=(0,n.useCallback)(e=>{let t=JSON.parse(JSON.stringify(T)),s=!1;for(let a of t.modules){for(let n of a.chapters){let l=n.contents.find(t=>t.id===e);if(l){l.isCompleted=!l.isCompleted,s=!0;let e=n.contents.filter(e=>e.isCompleted).length,r=n.order,i=a.chapters.find(e=>e.order===r+1);if(i&&e===n.contents.length&&n.quiz.isPassed&&(i.isUnlocked=!0),a.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed)&&a.moduleQuiz.isPassed){let e=a.order,s=t.modules.find(t=>t.order===e+1);s&&(s.isUnlocked=!0,s.chapters.length>0&&(s.chapters[0].isUnlocked=!0))}break}}if(s)break}w(t)},[T,w]),J=(0,n.useCallback)(e=>{let t;if(T.finalExam.id===e)return void s.push("/my-courses/".concat(g,"/exam?type=final&examId=").concat(e));for(let t of T.modules){for(let s of t.chapters)if(s.quiz.id===e)return void U({...s.quiz});if(t.moduleQuiz.id===e)return void U({...t.moduleQuiz})}},[T,g,s]),W=(0,n.useCallback)(e=>{if(!I)return;let t=JSON.parse(JSON.stringify(T)),s=t=>{t.attempts+=1,t.lastScore=e,t.isPassed=e>=t.minimumScore};for(let e of t.modules){for(let t of e.chapters)if(t.quiz.id===I.id){s(t.quiz);let a=t.contents.every(e=>e.isCompleted);if((t.quiz.isPassed||t.quiz.attempts>=t.quiz.maxAttempts)&&a){let s=e.chapters.find(e=>e.order===t.order+1);s&&(s.isUnlocked=!0)}break}if(e.moduleQuiz.id===I.id){s(e.moduleQuiz);let a=e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts));if((e.moduleQuiz.isPassed||e.moduleQuiz.attempts>=e.moduleQuiz.maxAttempts)&&a){let s=t.modules.find(t=>t.order===e.order+1);s&&(s.isUnlocked=!0,s.chapters.length>0&&(s.chapters[0].isUnlocked=!0))}}}if(t.finalExam.id===I.id){s(t.finalExam);let e=t.modules.every(e=>e.chapters.every(e=>e.contents.every(e=>e.isCompleted)&&(e.quiz.isPassed||e.quiz.attempts>=e.quiz.maxAttempts))&&(e.moduleQuiz.isPassed||e.moduleQuiz.attempts>=e.moduleQuiz.maxAttempts));t.finalExam.isPassed&&e&&(t.certificate.isEligible=!0,t.certificate.completionDate=new Date().toISOString().split("T")[0],t.status="completed")}w(t),U(null)},[I,T,w]),G=(0,n.useCallback)(()=>{T.certificate.isEligible&&(w({...T,certificate:{...T.certificate,isGenerated:!0,certificateUrl:"#certificate-".concat(T.id)}}),H(!0))},[T,w]),X=(0,n.useCallback)((e,t,s)=>{L("course"),q(t=>({...t,[e]:!0})),t&&E(e=>({...e,[t]:!0})),s&&S(e=>({...e,[s]:!0})),setTimeout(()=>{let a=document.getElementById(s?"content-".concat(s):t?"chapter-".concat(t):"module-".concat(e));a&&(a.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"}),a.classList.add("ring-2","ring-blue-400","ring-opacity-75"),setTimeout(()=>{a.classList.remove("ring-2","ring-blue-400","ring-opacity-75")},2e3))},100)},[]),ee=T.modules.reduce((e,t)=>e+t.chapters.filter(e=>e.contents.every(e=>e.isCompleted)&&e.quiz.isPassed).length,0),et=T.modules.reduce((e,t)=>e+t.chapters.length,0),es=et>0?ee/et*100:0;return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 overflow-auto","data-sentry-component":"CoursePage","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"mx-auto max-w-full space-y-6 p-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(j(),{href:"/my-courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(l.$,{variant:"outline",size:"sm",className:"flex items-center space-x-2","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(d.A,{className:"h-4 w-4","data-sentry-element":"ArrowLeftIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Kembali ke Kursus Saya"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(u,{className:"h-8 w-8 text-[var(--iai-primary)]","data-sentry-element":"BuildingIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:T.name}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Kode Kursus: ",T.code]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Instruktur: ",T.instructor]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,a.jsx)(x,{className:"mr-1 inline h-4 w-4","data-sentry-element":"CalendarIcon","data-sentry-source-file":"page.tsx"}),T.startDate," - ",T.endDate]}),(0,a.jsx)(c.E,{variant:"completed"===T.status?"default":"secondary","data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:"completed"===T.status?"Selesai":"Sedang Belajar"})]})]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Kemajuan Keseluruhan"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(r.k,{value:es,className:"w-32","data-sentry-element":"Progress","data-sentry-source-file":"page.tsx"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[Math.round(es),"%"]})]})]})})]}),(0,a.jsxs)(o.tU,{value:Q,onValueChange:L,className:"mb-6","data-sentry-element":"Tabs","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(o.j7,{className:"grid w-full grid-cols-4","data-sentry-element":"TabsList","data-sentry-source-file":"page.tsx",children:[(0,a.jsxs)(o.Xi,{value:"course",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(p.A,{className:"h-4 w-4","data-sentry-element":"BookOpenIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Konten Kursus"})]}),(0,a.jsxs)(o.Xi,{value:"progress",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(h,{className:"h-4 w-4","data-sentry-element":"BarChartIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Kemajuan"})]}),(0,a.jsxs)(o.Xi,{value:"exam",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(y.A,{className:"h-4 w-4","data-sentry-element":"TrophyIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Final Exam"})]}),(0,a.jsxs)(o.Xi,{value:"certificate",className:"flex items-center space-x-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(y.A,{className:"h-4 w-4","data-sentry-element":"AwardIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Sertifikat"})]})]}),(0,a.jsx)(o.av,{value:"course",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(el,{courseData:T,expandedModules:M,expandedChapters:D,expandedContents:P,onToggleModule:Z,onToggleChapter:O,onToggleContent:$,onToggleContentComplete:F,onStartQuiz:J,onNavigateToSection:X,onExpandAllModules:_,onCollapseAllModules:K,onExpandAllChaptersInModule:R,onCollapseAllChaptersInModule:V,"data-sentry-element":"CourseTab","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(o.av,{value:"progress",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(ei,{courseData:T,overallProgress:es,"data-sentry-element":"ProgressTab","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(o.av,{value:"exam",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(ec,{courseData:T,onStartQuiz:J,"data-sentry-element":"ExamTab","data-sentry-source-file":"page.tsx"})}),(0,a.jsx)(o.av,{value:"certificate",className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(ed,{courseData:T,institution:{id:"iai-indonesia",name:"Indonesian Institute of Architects",shortName:"IAI",website:"https://iai.or.id",certificateTemplate:{primaryColor:"#1e40af",secondaryColor:"#f59e0b",signatoryName:"Ar. Georgius Budi Yulianto, IAI, AA",signatoryTitle:"Ketua Umum IAI 2024-2027"}},overallProgress:es,onGenerateCertificate:G,onShowCertificate:()=>H(!0),"data-sentry-element":"CertificateTab","data-sentry-source-file":"page.tsx"})})]}),I&&(0,a.jsx)(Y,{quiz:I,isOpen:!0,onComplete:W,onClose:()=>U(null)}),(0,a.jsx)(i.lG,{open:B,onOpenChange:H,"data-sentry-element":"Dialog","data-sentry-source-file":"page.tsx",children:(0,a.jsx)(i.Cf,{className:"max-h-[90vh] max-w-7xl w-[95vw] p-0","data-sentry-element":"DialogContent","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)("div",{className:"flex h-full max-h-[90vh] flex-col",children:[(0,a.jsx)(i.c7,{className:"flex-shrink-0 border-b px-6 py-4","data-sentry-element":"DialogHeader","data-sentry-source-file":"page.tsx",children:(0,a.jsxs)(i.L3,{className:"flex items-center space-x-2","data-sentry-element":"DialogTitle","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(y.A,{className:"h-5 w-5","data-sentry-element":"AwardIcon","data-sentry-source-file":"page.tsx"}),(0,a.jsx)("span",{children:"Sertifikat Anda"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-6",children:(0,a.jsx)("div",{className:"w-full h-full",dangerouslySetInnerHTML:{__html:(0,v.nE)({studentName:(null==(e=C.qs.getUser())?void 0:e.name)||"John Doe",courseName:T.name,courseCode:T.code,completionDate:T.certificate.completionDate||new Date().toISOString().split("T")[0],finalScore:T.finalExam.lastScore||0,instructorName:T.instructor,institutionName:"Indonesian Institute of Architects",certificateId:(0,v.K2)()})}})}),(0,a.jsx)("div",{className:"flex-shrink-0 border-t bg-white px-6 py-4",children:(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)(l.$,{variant:"outline",onClick:()=>H(!1),"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:"Tutup"}),(0,a.jsxs)(l.$,{onClick:async()=>{var e;let t={studentName:(null==(e=C.qs.getUser())?void 0:e.name)||"John Doe",courseName:T.name,courseCode:T.code,completionDate:T.certificate.completionDate||new Date().toISOString().split("T")[0],finalScore:T.finalExam.lastScore||0,instructorName:T.instructor,institutionName:"Indonesian Institute of Architects",certificateId:(0,v.K2)()};await (0,v.Ct)(t)},"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,a.jsx)(f,{className:"mr-2 h-4 w-4","data-sentry-element":"DownloadIcon","data-sentry-source-file":"page.tsx"}),"Unduh PDF"]})]})})]})})})]})})}},61362:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69264:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(96063).A)("BookOpen01Icon",[["path",{d:"M12 6L12 20",stroke:"currentColor",key:"k0"}],["path",{d:"M5.98056 3.28544C9.32175 3.9216 11.3131 5.25231 12 6.01628C12.6869 5.25231 14.6782 3.9216 18.0194 3.28544C19.7121 2.96315 20.5584 2.80201 21.2792 3.41964C22 4.03727 22 5.04022 22 7.04612V14.255C22 16.0891 22 17.0061 21.5374 17.5787C21.0748 18.1512 20.0564 18.3451 18.0194 18.733C16.2037 19.0787 14.7866 19.6295 13.7608 20.1831C12.7516 20.7277 12.247 21 12 21C11.753 21 11.2484 20.7277 10.2392 20.1831C9.21344 19.6295 7.79633 19.0787 5.98056 18.733C3.94365 18.3451 2.9252 18.1512 2.4626 17.5787C2 17.0061 2 16.0891 2 14.255V7.04612C2 5.04022 2 4.03727 2.72078 3.41964C3.44157 2.80201 4.2879 2.96315 5.98056 3.28544Z",stroke:"currentColor",key:"k1"}]])},78874:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},85921:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},96439:(e,t,s)=>{Promise.resolve().then(s.bind(s,55170))},99708:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4909,7055,4736,660,6093,5239,5667,6464,4871,5521,5542,5467,2562,4850,8441,3840,7358],()=>t(96439)),_N_E=e.O()}]);