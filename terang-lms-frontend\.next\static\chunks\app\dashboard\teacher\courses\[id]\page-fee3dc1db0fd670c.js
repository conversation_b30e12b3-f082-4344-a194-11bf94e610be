try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},i=(new e.Error).stack;i&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[i]="76ec69f5-43ed-4400-916d-46359ee66e62",e._sentryDebugIdIdentifier="sentry-dbid-76ec69f5-43ed-4400-916d-46359ee66e62")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2533],{67243:(e,i,t)=>{Promise.resolve().then(t.bind(t,69580))},69580:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>q});var n=t(95155),o=t(12115),s=t(20063),a=t(66094),r=t(20764),d=t(88941),c=t(35626),l=t(6132),u=t(52619),m=t.n(u),p=t(47886),h=t(18720),f=t(73468);function x(e){return{id:e.id.toString(),type:e.type,question:e.question||[{type:"text",value:""}],options:e.options||void 0,essayAnswer:e.essayAnswer,explanation:e.explanation||void 0,points:e.points,orderIndex:e.orderIndex}}function z(e){return{id:e.id.toString(),name:e.name,description:e.description,questions:e.questions?e.questions.map(x):[],timeLimit:e.timeLimit,minimumScore:parseInt(e.minimumScore)}}function y(e){var i;let t=null==(i=e.quizzes)?void 0:i.find(e=>"chapter"===e.quizType),n=[];if(e.content)if("string"==typeof e.content)try{n=JSON.parse(e.content)}catch(e){console.error("Error parsing chapter content:",e),n=[]}else n=e.content;return{id:e.id.toString(),name:e.name,content:n,orderIndex:e.orderIndex,hasChapterQuiz:!!t,chapterQuiz:t?z(t):void 0}}function q(e){let{params:i}=e,{id:t}=(0,o.use)(i),u=(0,s.useRouter)(),[x,q]=(0,o.useState)(null),[g,I]=(0,o.useState)(!0),[j,E]=(0,o.useState)(null),[w,v]=(0,o.useState)(!1);(0,o.useEffect)(()=>{t&&N()},[t]);let N=async()=>{try{I(!0),E(null);let e=p.qs.getUser();if(!e)return void E("Please log in to edit course");let i=await fetch("/api/courses/".concat(t,"?teacherId=").concat(e.id)),n=await i.json();if(!i.ok)throw Error(n.error||"Failed to fetch course data");if(!n.success||!n.course)throw Error("Course not found");let o=n.course;console.log("Fetched course data:",o);let s=o.moduleQuizzes||[],a=s.find(e=>"final"===e.quizType),r=s.filter(e=>"module"===e.quizType);console.log("Quiz breakdown:",{totalQuizzes:s.length,moduleQuizzes:r.length,finalExam:a?a.id:"none",quizzes:s.map(e=>({id:e.id,type:e.quizType,moduleId:e.moduleId,courseId:e.courseId}))});let d=function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],t=arguments.length>2?arguments[2]:void 0;console.log("Transforming API course data:",{courseId:e.id,moduleQuizzesCount:i.length,finalExam:t?t.id:"none",allQuizzes:i.map(e=>({id:e.id,type:e.quizType,moduleId:e.moduleId,courseId:e.courseId}))});let n=i.filter(e=>"module"===e.quizType),o=t||i.find(e=>"final"===e.quizType);return console.log("Filtered quizzes:",{moduleQuizzes:n.length,finalExam:o?o.id:"none"}),{name:e.name,description:e.description,instructor:e.instructor||"",courseCode:e.courseCode,type:e.type,enrollmentType:e.enrollmentType||"code",startDate:e.startDate?new Date(e.startDate):void 0,endDate:e.endDate?new Date(e.endDate):void 0,coverImagePreview:e.coverImage||e.coverPicture,isPurchasable:e.isPurchasable,price:e.price,currency:e.currency,previewMode:e.previewMode,modules:e.modules.map(e=>(function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],t=i.find(i=>i.moduleId===e.id&&"module"===i.quizType);return console.log("Module ".concat(e.id,": Found module quiz:"),t),{id:e.id.toString(),name:e.name,description:e.description,orderIndex:e.orderIndex,chapters:e.chapters.map(y),hasModuleQuiz:!!t,moduleQuiz:t?z(t):void 0}})(e,i)),isPublished:!0,assignedClasses:[],finalExam:o?z(o):void 0,admissions:e.admissions?{requirements:e.admissions.requirements||[],applicationDeadline:e.admissions.applicationDeadline||"",prerequisites:e.admissions.prerequisites||[]}:void 0,academics:e.academics?{credits:e.academics.credits||0,workload:e.academics.workload||"",assessment:e.academics.assessment||[]}:void 0,tuitionAndFinancing:e.tuitionAndFinancing?{totalCost:e.tuitionAndFinancing.totalCost||0,paymentOptions:e.tuitionAndFinancing.paymentOptions||[],scholarships:e.tuitionAndFinancing.scholarships||[]}:void 0,careers:e.careers?{outcomes:e.careers.outcomes||[],industries:e.careers.industries||[],averageSalary:e.careers.averageSalary||""}:void 0,studentExperience:e.studentExperience?{testimonials:e.studentExperience.testimonials||[],facilities:e.studentExperience.facilities||[],support:e.studentExperience.support||[]}:void 0}}(o,s,a);console.log("Transformed data:",d),q(d)}catch(e){console.error("Error fetching course:",e),E(e instanceof Error?e.message:"Failed to fetch course data")}finally{I(!1)}},S=async e=>{try{var i,n,o,s,a;v(!0),console.log("Starting course update with data:",e);let r=p.qs.getUser();if(!r)return void h.oR.error("Please log in to update course");let d=(console.log("Transforming wizard data to API update:",{courseId:t,modulesCount:e.modules.length,finalExamExists:!!e.finalExam,moduleQuizzes:e.modules.filter(e=>e.hasModuleQuiz).length}),{courseId:parseInt(t),name:e.name,description:e.description,courseCode:e.courseCode,type:e.type,enrollmentType:e.enrollmentType,price:e.price,currency:e.currency,isPurchasable:"purchase"===e.enrollmentType||"both"===e.enrollmentType,startDate:null==(s=e.startDate)?void 0:s.toISOString(),endDate:null==(a=e.endDate)?void 0:a.toISOString(),modules:e.modules.map(e=>{var i;return console.log("Transforming module ".concat(e.name,":"),{id:e.id,hasModuleQuiz:e.hasModuleQuiz,moduleQuizId:null==(i=e.moduleQuiz)?void 0:i.id}),{id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,name:e.name,description:e.description,orderIndex:e.orderIndex,chapters:e.chapters.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,name:e.name,content:e.content,orderIndex:e.orderIndex,quiz:e.hasChapterQuiz&&e.chapterQuiz?{id:e.chapterQuiz.id&&!isNaN(parseInt(e.chapterQuiz.id))?parseInt(e.chapterQuiz.id):void 0,name:e.chapterQuiz.name,description:e.chapterQuiz.description,quizType:"chapter",timeLimit:e.chapterQuiz.timeLimit,minimumScore:e.chapterQuiz.minimumScore,questions:e.chapterQuiz.questions.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,type:e.type,question:e.question,options:e.options||null,essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation||null,points:e.points,orderIndex:e.orderIndex}))}:void 0})),quiz:e.hasModuleQuiz&&e.moduleQuiz?{id:e.moduleQuiz.id&&!isNaN(parseInt(e.moduleQuiz.id))?parseInt(e.moduleQuiz.id):void 0,name:e.moduleQuiz.name,description:e.moduleQuiz.description,quizType:"module",timeLimit:e.moduleQuiz.timeLimit,minimumScore:e.moduleQuiz.minimumScore,questions:e.moduleQuiz.questions.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,type:e.type,question:e.question,options:e.options||null,essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation||null,points:e.points,orderIndex:e.orderIndex}))}:void 0}}),finalExam:e.finalExam?{id:e.finalExam.id&&!isNaN(parseInt(e.finalExam.id))?parseInt(e.finalExam.id):void 0,name:e.finalExam.name,description:e.finalExam.description,quizType:"final",timeLimit:e.finalExam.timeLimit,minimumScore:e.finalExam.minimumScore,questions:e.finalExam.questions.map(e=>({id:e.id&&!isNaN(parseInt(e.id))?parseInt(e.id):void 0,type:e.type,question:e.question,options:e.options||null,essayAnswer:"essay"===e.type?e.essayAnswer:null,explanation:e.explanation||null,points:e.points,orderIndex:e.orderIndex}))}:void 0,admissions:e.admissions,academics:e.academics,tuitionAndFinancing:e.tuitionAndFinancing,careers:e.careers,studentExperience:e.studentExperience});console.log("Update data prepared:",d);let c=await fetch("/api/courses/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:d.name,description:d.description,courseCode:d.courseCode,type:d.type,enrollmentType:d.enrollmentType,price:d.price,currency:d.currency,isPurchasable:d.isPurchasable,startDate:d.startDate,endDate:d.endDate,teacherId:r.id,admissions:d.admissions,academics:d.academics,tuitionAndFinancing:d.tuitionAndFinancing,careers:d.careers,studentExperience:d.studentExperience})});if(!c.ok){let e=await c.json();throw Error(e.error||"Failed to update course")}for(let e of d.modules){console.log("Processing module: ".concat(e.name),{id:e.id,hasModuleQuiz:!!e.quiz,moduleQuizId:null==(i=e.quiz)?void 0:i.id});let o=e.id;if(e.id)(await fetch("/api/modules/".concat(e.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,description:e.description,orderIndex:e.orderIndex,teacherId:r.id})})).ok||console.error("Failed to update module:",e.id);else{let i=await fetch("/api/modules",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,description:e.description,orderIndex:e.orderIndex,courseId:parseInt(t),teacherId:r.id})});i.ok&&(o=(await i.json()).module.id,console.log("Created new module with ID:",o))}for(let i of e.chapters){let e=i.id;if(i.id)(await fetch("/api/chapters/".concat(i.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,content:i.content,orderIndex:i.orderIndex,teacherId:r.id})})).ok||console.error("Failed to update chapter:",i.id);else{let t=await fetch("/api/chapters",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.name,content:i.content,orderIndex:i.orderIndex,moduleId:o,teacherId:r.id})});t.ok&&(e=(await t.json()).chapter.id,console.log("Created new chapter with ID:",e))}if(i.quiz)if(console.log("Processing chapter quiz for chapter ".concat(e,":"),i.quiz.id),i.quiz.id){let e=await fetch("/api/quizzes/".concat(i.quiz.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.quiz.name,description:i.quiz.description,quizType:"chapter",timeLimit:i.quiz.timeLimit,minimumScore:i.quiz.minimumScore,teacherId:r.id,questions:i.quiz.questions})});if(!e.ok){console.error("Failed to update chapter quiz:",i.quiz.id);let t=await e.json();console.error("Chapter quiz update error:",t)}}else{let t=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:i.quiz.name,description:i.quiz.description,quizType:"chapter",timeLimit:i.quiz.timeLimit,minimumScore:i.quiz.minimumScore,chapterId:e,teacherId:r.id,questions:i.quiz.questions})});if(!t.ok){console.error("Failed to create chapter quiz");let e=await t.json();console.error("Chapter quiz creation error:",e)}}}if(e.quiz)if(console.log("Processing module quiz for module ".concat(o,":"),{quizId:e.quiz.id,quizName:e.quiz.name,questionsCount:e.quiz.questions.length}),e.quiz.id){let i=await fetch("/api/quizzes/".concat(e.quiz.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.quiz.name,description:e.quiz.description,quizType:"module",timeLimit:e.quiz.timeLimit,minimumScore:e.quiz.minimumScore,moduleId:o,teacherId:r.id,questions:e.quiz.questions})});if(i.ok)console.log("Successfully updated module quiz:",e.quiz.id);else{console.error("Failed to update module quiz:",e.quiz.id);let t=await i.json();console.error("Module quiz update error:",t)}}else{let i=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.quiz.name,description:e.quiz.description,quizType:"module",timeLimit:e.quiz.timeLimit,minimumScore:e.quiz.minimumScore,moduleId:o,teacherId:r.id,questions:e.quiz.questions})});if(i.ok){let e=await i.json();console.log("Successfully created module quiz:",null==(n=e.quiz)?void 0:n.id)}else{console.error("Failed to create module quiz");let e=await i.json();console.error("Module quiz creation error:",e)}}}if(d.finalExam)if(console.log("Processing final exam:",{examId:d.finalExam.id,examName:d.finalExam.name,questionsCount:d.finalExam.questions.length}),d.finalExam.id){let e=await fetch("/api/quizzes/".concat(d.finalExam.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:d.finalExam.name,description:d.finalExam.description,quizType:"final",timeLimit:d.finalExam.timeLimit,minimumScore:d.finalExam.minimumScore,courseId:parseInt(t),teacherId:r.id,questions:d.finalExam.questions})});if(e.ok)console.log("Successfully updated final exam:",d.finalExam.id);else{console.error("Failed to update final exam:",d.finalExam.id);let i=await e.json();console.error("Final exam update error:",i)}}else{let e=await fetch("/api/quizzes",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:d.finalExam.name,description:d.finalExam.description,quizType:"final",timeLimit:d.finalExam.timeLimit,minimumScore:d.finalExam.minimumScore,courseId:parseInt(t),teacherId:r.id,questions:d.finalExam.questions})});if(e.ok){let i=await e.json();console.log("Successfully created final exam:",null==(o=i.quiz)?void 0:o.id)}else{console.error("Failed to create final exam");let i=await e.json();console.error("Final exam creation error:",i)}}h.oR.success("Course updated successfully!"),u.push("/dashboard/teacher/courses")}catch(e){console.error("Error updating course:",e),h.oR.error(e instanceof Error?e.message:"Failed to update course")}finally{v(!1)}};return g?(0,n.jsxs)("div",{className:"container mx-auto p-6",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(d.E,{className:"h-8 w-48 mb-2"}),(0,n.jsx)(d.E,{className:"h-4 w-96"})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsxs)(a.aR,{children:[(0,n.jsx)(d.E,{className:"h-6 w-32"}),(0,n.jsx)(d.E,{className:"h-4 w-64"})]}),(0,n.jsx)(a.Wu,{children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(d.E,{className:"h-10 w-full"}),(0,n.jsx)(d.E,{className:"h-20 w-full"}),(0,n.jsx)(d.E,{className:"h-10 w-full"})]})})]})]}):j?(0,n.jsxs)("div",{className:"container mx-auto p-6",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(m(),{href:"/dashboard/teacher/courses",children:(0,n.jsxs)(r.$,{variant:"ghost",className:"mb-4",children:[(0,n.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Back to Courses"]})}),(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Course"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Update course information and content"})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsxs)(a.aR,{children:[(0,n.jsxs)(a.ZB,{className:"flex items-center gap-2 text-destructive",children:[(0,n.jsx)(l.A,{className:"h-5 w-5"}),"Error Loading Course"]}),(0,n.jsx)(a.BT,{children:j})]}),(0,n.jsx)(a.Wu,{children:(0,n.jsx)(r.$,{onClick:N,variant:"outline",children:"Try Again"})})]})]}):x?(0,n.jsxs)("div",{className:"container mx-auto p-6","data-sentry-component":"CourseEditPage","data-sentry-source-file":"page.tsx",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(m(),{href:"/dashboard/teacher/courses","data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:(0,n.jsxs)(r.$,{variant:"ghost",className:"mb-4","data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,n.jsx)(c.A,{className:"h-4 w-4 mr-2","data-sentry-element":"ArrowLeft","data-sentry-source-file":"page.tsx"}),"Back to Courses"]})}),(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Course"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Update course information and content"})]}),(0,n.jsx)(f.q,{initialData:x,onComplete:S,onCancel:()=>{u.push("/dashboard/teacher/courses")},"data-sentry-element":"CourseCreationWizard","data-sentry-source-file":"page.tsx"})]}):(0,n.jsxs)("div",{className:"container mx-auto p-6",children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)(m(),{href:"/dashboard/teacher/courses",children:(0,n.jsxs)(r.$,{variant:"ghost",className:"mb-4",children:[(0,n.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Back to Courses"]})}),(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Course"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Update course information and content"})]}),(0,n.jsxs)(a.Zp,{children:[(0,n.jsxs)(a.aR,{children:[(0,n.jsx)(a.ZB,{children:"Course Not Found"}),(0,n.jsx)(a.BT,{children:"The requested course could not be found."})]}),(0,n.jsx)(a.Wu,{children:(0,n.jsx)(m(),{href:"/dashboard/teacher/courses",children:(0,n.jsx)(r.$,{children:"Back to Courses"})})})]})]})}},88941:(e,i,t)=>{"use strict";t.d(i,{E:()=>s});var n=t(95155),o=t(64269);function s(e){let{className:i,...t}=e;return(0,n.jsx)("div",{"data-slot":"skeleton",className:(0,o.cn)("bg-accent animate-pulse rounded-md",i),...t,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}}},e=>{var i=i=>e(e.s=i);e.O(0,[5105,4909,7055,4736,660,8720,6093,9568,5239,5667,6464,5439,4871,7392,3500,1569,4850,8441,3840,7358],()=>i(67243)),_N_E=e.O()}]);